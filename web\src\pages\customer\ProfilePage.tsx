import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit3,
  Save,
  X,
  Camera,
  Settings,
  Bell,
  Shield,
  LogOut,
  ChevronRight,
  Eye,
  EyeOff,
  Award,
  Star,
  Heart,
  Package,
  Clock,
  TrendingUp,
  Gift,
  Crown,
  Zap,
  Activity,
  BarChart3,
  Download,
  Share2,
  Truck,
  ShoppingBag,
  CheckCircle,
  Sparkles,
  Target,
  Flame,
  Trophy,
  Gem,
  Rocket,
  Globe,
  Palette,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Smartphone,
  CreditCard,
  HelpCircle,
  Home,
  Map,
  ArrowLeft,
  FileText,
  Bookmark,
  MessageSquare,
  ThumbsUp,
  Coffee,
  Navigation,
  Layers,
  Briefcase,
  PieChart,
  TrendingDown,
  Users,
  MapIcon,
  Headphones,
  Wifi,
  Battery,
  Signal
} from 'lucide-react';
import Logo from '../../components/common/Logo';
import { useAuth } from '../../contexts/AuthContext';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';

interface EditFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  address: string;
  city: string;
  country: string;
  dateOfBirth: string;
  gender: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const ProfilePage: React.FC = () => {
  const { user, isLoading, loadUserProfile } = useCurrentUserData();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [editMode, setEditMode] = useState<'profile' | 'password' | null>(null);
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'stats' | 'achievements' | 'settings'>('overview');
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Enhanced UI states
  const [notificationsEnabled, setNotificationsEnabled] = useState(user?.notifications ?? true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showAchievements, setShowAchievements] = useState(false);
  const [selectedAchievement, setSelectedAchievement] = useState<any>(null);



  // Enhanced user stats with impressive data
  const userStats = {
    totalOrders: 47,
    completedDeliveries: 42,
    totalSpent: 2847.50,
    loyaltyPoints: 3240,
    memberSince: new Date(user?.createdAt || '2023-01-15').getFullYear().toString(),
    favoriteSuppliers: 12,
    averageRating: 4.9,
    savedAddresses: 4,
    monthlyOrders: 8,
    weeklyOrders: 2,
    streakDays: 15,
    totalReviews: 28,
    helpfulReviews: 24,
    profileViews: 156,
    referrals: 7,
    carbonSaved: 12.5, // kg CO2
    distanceTraveled: 245.8, // km
    timesSaved: 18.5, // hours
    nextLevelProgress: 75, // percentage to VIP
  };

  // Enhanced achievements system
  const achievements = [
    {
      id: 1,
      name: 'First Order',
      description: 'Placed your first order',
      icon: Package,
      earned: true,
      date: '2023-01-15',
      color: 'from-blue-500 to-blue-600',
      rarity: 'common'
    },
    {
      id: 2,
      name: 'Loyal Customer',
      description: 'Completed 25+ orders',
      icon: Heart,
      earned: true,
      date: '2023-06-20',
      color: 'from-red-500 to-red-600',
      rarity: 'rare'
    },
    {
      id: 3,
      name: 'Speed Demon',
      description: 'Ordered 5 times in one day',
      icon: Zap,
      earned: true,
      date: '2023-08-10',
      color: 'from-yellow-500 to-yellow-600',
      rarity: 'epic'
    },
    {
      id: 4,
      name: 'VIP Member',
      description: 'Reach VIP status',
      icon: Crown,
      earned: false,
      date: null,
      color: 'from-purple-500 to-purple-600',
      rarity: 'legendary'
    },
    {
      id: 5,
      name: 'Review Master',
      description: 'Left 20+ helpful reviews',
      icon: Star,
      earned: true,
      date: '2023-09-05',
      color: 'from-green-500 to-green-600',
      rarity: 'rare'
    },
    {
      id: 6,
      name: 'Explorer',
      description: 'Ordered from 10+ different suppliers',
      icon: MapIcon,
      earned: true,
      date: '2023-10-12',
      color: 'from-teal-500 to-teal-600',
      rarity: 'epic'
    },
    {
      id: 7,
      name: 'Eco Warrior',
      description: 'Saved 10kg+ of CO2',
      icon: Sparkles,
      earned: true,
      date: '2023-11-01',
      color: 'from-emerald-500 to-emerald-600',
      rarity: 'epic'
    },
    {
      id: 8,
      name: 'Night Owl',
      description: 'Ordered after midnight 5+ times',
      icon: Moon,
      earned: false,
      date: null,
      color: 'from-indigo-500 to-indigo-600',
      rarity: 'rare'
    }
  ];

  // Load user profile on component mount
  useEffect(() => {
    loadUserProfile();
  }, [loadUserProfile]);

  // Override body background and ensure full width for profile page
  useEffect(() => {
    // Store original styles
    const originalBodyBackground = document.body.style.backgroundColor;
    const originalBodyMargin = document.body.style.margin;
    const originalBodyPadding = document.body.style.padding;
    const originalHtmlBackground = document.documentElement.style.backgroundColor;

    // Set new styles for profile page
    document.body.style.backgroundColor = 'transparent';
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.documentElement.style.backgroundColor = 'transparent';

    // Ensure root element is full width
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.style.maxWidth = 'none';
      rootElement.style.margin = '0';
      rootElement.style.padding = '0';
      rootElement.style.width = '100vw';
    }

    // Hide navigation for full screen experience
    const navElement = document.querySelector('nav');
    const mainElement = document.querySelector('main');
    if (navElement) {
      navElement.style.display = 'none';
    }
    if (mainElement) {
      mainElement.style.paddingTop = '0';
    }

    // Cleanup function to restore original styles
    return () => {
      document.body.style.backgroundColor = originalBodyBackground;
      document.body.style.margin = originalBodyMargin;
      document.body.style.padding = originalBodyPadding;
      document.documentElement.style.backgroundColor = originalHtmlBackground;

      if (rootElement) {
        rootElement.style.maxWidth = '';
        rootElement.style.margin = '';
        rootElement.style.padding = '';
        rootElement.style.width = '';
      }

      // Restore navigation
      if (navElement) {
        navElement.style.display = '';
      }
      if (mainElement) {
        mainElement.style.paddingTop = '';
      }
    };
  }, []);

  // Handle scroll for header animation
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      setIsHeaderCompact(currentScrollY > 120);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Profile edit form with real user data
  const [profileForm, setProfileForm] = useState<EditFormData>({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phoneNumber: user?.phoneNumber || '',
    address: user?.address || '',
    city: user?.city || '',
    country: user?.country || '',
    dateOfBirth: user?.dateOfBirth || '',
    gender: user?.gender || ''
  });

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      setProfileForm({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phoneNumber: user.phoneNumber || '',
        address: user.address || '',
        city: user.city || '',
        country: user.country || '',
        dateOfBirth: user.dateOfBirth || '',
        gender: user.gender || ''
      });
      setNotificationsEnabled(user.notifications);
    }
  }, [user]);

  // Password change form
  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleProfileUpdate = async () => {
    setIsUpdating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Profile updated:', profileForm);
      setEditMode(null);
    } catch (error) {
      console.error('Profile update failed:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    setIsUpdating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Password changed');
      setEditMode(null);
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error) {
      console.error('Password change failed:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLogout = async () => {
    if (window.confirm('Are you sure you want to logout?')) {
      // Clear user data and redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full mx-auto mb-4"
          />
          <p className="text-gray-600 text-lg">Loading your amazing profile...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen w-full relative overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30"
      style={{
        margin: 0,
        padding: 0,
        width: '100vw',
        position: 'relative',
        left: '50%',
        right: '50%',
        marginLeft: '-50vw',
        marginRight: '-50vw'
      }}
    >
      {/* Floating Navigation Bar */}
      <motion.nav
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="fixed top-4 left-4 right-4 bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 z-50"
      >
        <div className="flex justify-between items-center px-6 py-4">
          <div className="flex items-center space-x-4">
            <Logo size="sm" />
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <button
              onClick={() => navigate('/customer/home')}
              className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200"
            >
              <Home size={18} />
              <span>Home</span>
            </button>
            <button
              onClick={() => navigate('/customer/orders')}
              className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200"
            >
              <ShoppingBag size={18} />
              <span>Orders</span>
            </button>
            <button
              onClick={() => navigate('/customer/packages')}
              className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200"
            >
              <Package size={18} />
              <span>Packages</span>
            </button>
            <button
              onClick={() => navigate('/customer/suppliers-map')}
              className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200"
            >
              <Map size={18} />
              <span>Map</span>
            </button>
          </div>

          <button
            onClick={logout}
            className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-600 hover:text-red-600 hover:bg-red-50 transition-all duration-200"
          >
            <LogOut size={18} />
            <span className="hidden md:inline">Logout</span>
          </button>
        </div>
      </motion.nav>

      {/* Ultra Premium Animated Background */}
      <div className="fixed inset-0 overflow-hidden">
        {/* Main gradient orbs with enhanced animations */}
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.8, 0.4],
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-primary-400/40 to-secondary-400/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 0.8, 1.2],
            opacity: [0.3, 0.7, 0.3],
            x: [0, -80, 0],
            y: [0, 60, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3,
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-secondary-400/40 to-primary-400/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.5, 0.9, 0.5],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 6,
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-full blur-3xl"
        />

        {/* Floating particles with enhanced movement */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-primary-400/60 rounded-full"
            style={{
              left: `${10 + i * 8}%`,
              top: `${20 + (i % 3) * 20}%`,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.sin(i) * 20, 0],
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 4 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.3,
            }}
          />
        ))}

        {/* Geometric shapes */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-20 h-20 border-2 border-primary-300/50 rotate-45"
          animate={{
            rotate: [45, 225, 45],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/4 w-16 h-16 bg-secondary-300/30 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.8, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        />
      </div>

      {/* Ultra Enhanced Main Content */}
      <div className="relative z-10 min-h-screen w-full pt-24">
        {/* Revolutionary Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className={`transition-all duration-500 ${
            isHeaderCompact ? 'py-4' : 'py-16'
          } bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 text-white relative overflow-hidden`}
          style={{
            transform: `translateY(${scrollY * 0.1}px)`,
          }}
        >
          {/* Ultra Enhanced Decorative Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              animate={{
                scale: [1, 1.3, 1],
                rotate: [0, 360],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                ease: "linear",
              }}
              className="absolute top-0 right-0 w-80 h-80 bg-white/20 rounded-full"
              style={{ transform: 'translate(40%, -40%)' }}
            />
            <motion.div
              animate={{
                scale: [1.2, 0.8, 1.2],
                rotate: [360, 0],
                opacity: [0.2, 0.4, 0.2],
              }}
              transition={{
                duration: 30,
                repeat: Infinity,
                ease: "linear",
              }}
              className="absolute bottom-0 left-0 w-64 h-64 bg-white/15 rounded-full"
              style={{ transform: 'translate(-40%, 40%)' }}
            />

            {/* Floating geometric shapes */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-12 h-12 border-2 border-white/30"
              animate={{
                rotate: [0, 180, 360],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute top-3/4 right-1/3 w-8 h-8 bg-white/20 rounded-full"
              animate={{
                y: [0, -20, 0],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </div>

          <div className="relative z-10 w-full px-8">
            <div className="flex flex-col xl:flex-row items-center gap-12">
              {/* Ultra Enhanced Avatar Section */}
              <div className="relative group">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Avatar glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/30 to-white/10 rounded-full blur-xl scale-110"></div>

                  {/* Main avatar */}
                  <div className="relative w-40 h-40 bg-gradient-to-br from-white/25 to-white/10 rounded-full flex items-center justify-center text-4xl font-bold backdrop-blur-sm border-4 border-white/40 shadow-2xl">
                    {getInitials(user?.firstName, user?.lastName)}

                    {/* Rotating border effect */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-4 border-transparent bg-gradient-to-r from-white/50 via-transparent to-white/50"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      style={{
                        background: 'conic-gradient(from 0deg, transparent, white, transparent)',
                        WebkitMask: 'radial-gradient(circle, transparent 50%, black 52%)',
                        mask: 'radial-gradient(circle, transparent 50%, black 52%)'
                      }}
                    />
                  </div>
                </motion.div>

                {/* Enhanced Camera Button */}
                <motion.button
                  className="absolute bottom-3 right-3 w-14 h-14 bg-gradient-to-r from-white to-gray-100 rounded-full flex items-center justify-center hover:from-gray-100 hover:to-white transition-all duration-300 shadow-xl group"
                  whileHover={{ scale: 1.15, rotate: 15 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Camera size={22} className="text-gray-700 group-hover:text-primary-600 transition-colors" />
                </motion.button>

                {/* Enhanced Status Indicators */}
                <motion.div
                  className="absolute top-3 right-3 w-8 h-8 bg-gradient-to-r from-green-400 to-green-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <div className="w-3 h-3 bg-green-600 rounded-full animate-pulse"></div>
                </motion.div>

                {/* VIP Badge */}
                {userStats.nextLevelProgress > 70 && (
                  <motion.div
                    className="absolute top-3 left-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold shadow-lg"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1, type: "spring" }}
                  >
                    VIP
                  </motion.div>
                )}
              </div>

              {/* Ultra Enhanced User Information */}
              <div className="text-center xl:text-left flex-1 space-y-6">
                <div>
                  <motion.h1
                    className="text-5xl xl:text-6xl font-bold mb-4 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2, duration: 0.8 }}
                  >
                    {user?.firstName} {user?.lastName}
                  </motion.h1>

                  <motion.div
                    className="space-y-3 text-white/90"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                  >
                    <div className="flex flex-col xl:flex-row xl:items-center gap-2 xl:gap-6 text-lg">
                      <span className="flex items-center gap-2">
                        <User size={18} />
                        @{user?.username}
                      </span>
                      <span className="flex items-center gap-2">
                        <Mail size={18} />
                        {user?.email}
                      </span>
                      <span className="flex items-center gap-2">
                        <Phone size={18} />
                        {user?.phoneNumber}
                      </span>
                    </div>
                    <div className="flex items-center justify-center xl:justify-start gap-2 text-white/70">
                      <Calendar size={16} />
                      <span>Member since {userStats.memberSince}</span>
                    </div>
                  </motion.div>
                </div>

                {/* Ultra Enhanced Stats Grid */}
                <motion.div
                  className="grid grid-cols-2 md:grid-cols-4 gap-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  {[
                    { label: 'Orders', value: userStats.totalOrders, icon: Package, color: 'from-blue-400 to-blue-500' },
                    { label: 'Points', value: userStats.loyaltyPoints, icon: Star, color: 'from-yellow-400 to-yellow-500' },
                    { label: 'Rating', value: userStats.averageRating, icon: Heart, color: 'from-red-400 to-red-500' },
                    { label: 'Streak', value: `${userStats.streakDays}d`, icon: Flame, color: 'from-orange-400 to-orange-500' }
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      className="text-center group cursor-pointer"
                      whileHover={{ scale: 1.05, y: -5 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className={`w-16 h-16 mx-auto mb-2 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow`}>
                        <stat.icon size={24} className="text-white" />
                      </div>
                      <div className="text-2xl font-bold">{stat.value}</div>
                      <div className="text-white/70 text-sm">{stat.label}</div>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Progress to next level */}
                <motion.div
                  className="bg-white/10 backdrop-blur-sm rounded-2xl p-4"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/90 font-medium">Progress to VIP</span>
                    <span className="text-white font-bold">{userStats.nextLevelProgress}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${userStats.nextLevelProgress}%` }}
                      transition={{ delay: 0.8, duration: 1.5, ease: "easeOut" }}
                    />
                  </div>
                </motion.div>
              </div>

              {/* Quick Action Buttons */}
              <motion.div
                className="flex flex-col gap-4"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.8 }}
              >
                {[
                  { icon: Share2, label: 'Share', color: 'from-blue-500 to-blue-600' },
                  { icon: Download, label: 'Export', color: 'from-green-500 to-green-600' },
                  { icon: Settings, label: 'Settings', color: 'from-purple-500 to-purple-600' }
                ].map((action) => (
                  <motion.button
                    key={action.label}
                    className={`px-6 py-3 bg-gradient-to-r ${action.color} hover:shadow-lg rounded-xl backdrop-blur-sm border border-white/20 transition-all duration-300 flex items-center gap-3 min-w-[120px]`}
                    whileHover={{ scale: 1.05, x: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <action.icon size={18} />
                    <span className="font-medium">{action.label}</span>
                  </motion.button>
                ))}
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Ultra Enhanced Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="w-full px-8 py-8"
        >
          <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-3 shadow-2xl border border-white/20 mb-8">
            <div className="flex flex-wrap gap-3">
              {[
                { id: 'overview', label: 'Overview', icon: User, gradient: 'from-blue-500 to-blue-600' },
                { id: 'stats', label: 'Statistics', icon: BarChart3, gradient: 'from-green-500 to-green-600' },
                { id: 'achievements', label: 'Achievements', icon: Award, gradient: 'from-purple-500 to-purple-600' },
                { id: 'settings', label: 'Settings', icon: Settings, gradient: 'from-gray-500 to-gray-600' }
              ].map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-3 px-6 py-4 rounded-2xl font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg scale-105`
                      : 'text-gray-600 hover:bg-gray-100 hover:scale-102'
                  }`}
                  whileHover={{ scale: activeTab === tab.id ? 1.05 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <tab.icon size={20} />
                  <span className="font-semibold">{tab.label}</span>
                  {activeTab === tab.id && (
                    <motion.div
                      className="w-2 h-2 bg-white rounded-full"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 500 }}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </div>

          {/* Ultra Enhanced Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'overview' && (
              <motion.div
                key="overview"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                {/* Single Column Layout - Full Width */}
                <div className="max-w-6xl mx-auto space-y-8">
              {/* Personal Information Card */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 }}
                  >
                    <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                      <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-primary-50 to-secondary-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <motion.div
                              className="w-14 h-14 rounded-xl flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 shadow-lg"
                              whileHover={{ scale: 1.05, rotate: 5 }}
                            >
                              <User className="text-white" size={24} />
                            </motion.div>
                            <div>
                              <h3 className="font-bold text-xl text-gray-900">Personal Information</h3>
                              <p className="text-gray-600">Manage your personal details and preferences</p>
                            </div>
                          </div>
                          <motion.button
                            onClick={() => setEditMode(editMode === 'profile' ? null : 'profile')}
                            className="p-3 hover:bg-white/80 rounded-xl transition-all duration-300 group"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {editMode === 'profile' ? (
                              <X size={20} className="text-gray-600 group-hover:text-red-500" />
                            ) : (
                              <Edit3 size={20} className="text-gray-600 group-hover:text-primary-600" />
                            )}
                          </motion.button>
                        </div>
                      </div>

                      <div className="p-6">
                        <AnimatePresence mode="wait">
                          {editMode === 'profile' ? (
                            <motion.div
                              key="edit-form"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="space-y-6"
                            >
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                  <input
                                    type="text"
                                    value={profileForm.firstName}
                                    onChange={(e) => setProfileForm({...profileForm, firstName: e.target.value})}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                  <input
                                    type="text"
                                    value={profileForm.lastName}
                                    onChange={(e) => setProfileForm({...profileForm, lastName: e.target.value})}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                  />
                                </div>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input
                                  type="tel"
                                  value={profileForm.phoneNumber}
                                  onChange={(e) => setProfileForm({...profileForm, phoneNumber: e.target.value})}
                                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <input
                                  type="text"
                                  value={profileForm.address}
                                  onChange={(e) => setProfileForm({...profileForm, address: e.target.value})}
                                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                />
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                                  <input
                                    type="text"
                                    value={profileForm.city}
                                    onChange={(e) => setProfileForm({...profileForm, city: e.target.value})}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                                  <input
                                    type="text"
                                    value={profileForm.country}
                                    onChange={(e) => setProfileForm({...profileForm, country: e.target.value})}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                                  />
                                </div>
                              </div>

                              <div className="flex gap-3 pt-6">
                                <motion.button
                                  onClick={handleProfileUpdate}
                                  disabled={isUpdating}
                                  className="flex-1 px-6 py-3 bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-medium rounded-xl transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 shadow-lg"
                                  whileHover={{ scale: 1.02 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  {isUpdating ? (
                                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                  ) : (
                                    <Save size={18} />
                                  )}
                                  {isUpdating ? 'Saving...' : 'Save Changes'}
                                </motion.button>
                                <motion.button
                                  onClick={() => setEditMode(null)}
                                  className="px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-xl transition-all duration-300"
                                  whileHover={{ scale: 1.02 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  Cancel
                                </motion.button>
                              </div>
                            </motion.div>
                          ) : (
                            <motion.div
                              key="view-info"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="space-y-4"
                            >
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <motion.div
                                  className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-xl border border-blue-200/50"
                                  whileHover={{ scale: 1.02 }}
                                >
                                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <User size={18} className="text-white" />
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-600 font-medium">Full Name</p>
                                    <p className="font-semibold text-gray-900">{user?.firstName} {user?.lastName}</p>
                                  </div>
                                </motion.div>

                                <motion.div
                                  className="flex items-center gap-4 p-4 bg-gradient-to-r from-green-50 to-green-100/50 rounded-xl border border-green-200/50"
                                  whileHover={{ scale: 1.02 }}
                                >
                                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                    <Phone size={18} className="text-white" />
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-600 font-medium">Phone Number</p>
                                    <p className="font-semibold text-gray-900">{user?.phoneNumber}</p>
                                  </div>
                                </motion.div>
                              </div>

                              <motion.div
                                className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-xl border border-purple-200/50"
                                whileHover={{ scale: 1.02 }}
                              >
                                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                  <Mail size={18} className="text-white" />
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600 font-medium">Email Address</p>
                                  <p className="font-semibold text-gray-900">{user?.email}</p>
                                </div>
                              </motion.div>

                              <motion.div
                                className="flex items-center gap-4 p-4 bg-gradient-to-r from-orange-50 to-orange-100/50 rounded-xl border border-orange-200/50"
                                whileHover={{ scale: 1.02 }}
                              >
                                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                                  <MapPin size={18} className="text-white" />
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600 font-medium">Location</p>
                                  <p className="font-semibold text-gray-900">{user?.address}, {user?.city}, {user?.country}</p>
                                </div>
                              </motion.div>

                              {user?.dateOfBirth && (
                                <motion.div
                                  className="flex items-center gap-4 p-4 bg-gradient-to-r from-pink-50 to-pink-100/50 rounded-xl border border-pink-200/50"
                                  whileHover={{ scale: 1.02 }}
                                >
                                  <div className="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center">
                                    <Calendar size={18} className="text-white" />
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-600 font-medium">Date of Birth</p>
                                    <p className="font-semibold text-gray-900">{new Date(user.dateOfBirth).toLocaleDateString()}</p>
                                  </div>
                                </motion.div>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                  </motion.div>

                  {/* Security Settings Card */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden"
                  >
                    <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-red-50 to-red-100">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <motion.div
                            className="w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600 shadow-lg"
                            whileHover={{ scale: 1.05, rotate: 5 }}
                          >
                            <Shield className="text-white" size={20} />
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-lg text-gray-900">Security Settings</h3>
                            <p className="text-gray-600 text-sm">Manage your password and security</p>
                          </div>
                        </div>
                        <motion.button
                          onClick={() => setEditMode(editMode === 'password' ? null : 'password')}
                          className="p-2 hover:bg-white/80 rounded-xl transition-all duration-300"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {editMode === 'password' ? (
                            <X size={18} className="text-gray-600" />
                          ) : (
                            <Edit3 size={18} className="text-gray-600" />
                          )}
                        </motion.button>
                      </div>
                    </div>

                    <div className="p-6">
                      <AnimatePresence mode="wait">
                        {editMode === 'password' ? (
                          <motion.div
                            key="password-form"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="space-y-4"
                          >
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                              <div className="relative">
                                <input
                                  type={showPassword.current ? "text" : "password"}
                                  value={passwordForm.currentPassword}
                                  onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowPassword({...showPassword, current: !showPassword.current})}
                                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                >
                                  {showPassword.current ? <EyeOff size={16} /> : <Eye size={16} />}
                                </button>
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                              <div className="relative">
                                <input
                                  type={showPassword.new ? "text" : "password"}
                                  value={passwordForm.newPassword}
                                  onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowPassword({...showPassword, new: !showPassword.new})}
                                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                >
                                  {showPassword.new ? <EyeOff size={16} /> : <Eye size={16} />}
                                </button>
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                              <div className="relative">
                                <input
                                  type={showPassword.confirm ? "text" : "password"}
                                  value={passwordForm.confirmPassword}
                                  onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowPassword({...showPassword, confirm: !showPassword.confirm})}
                                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                >
                                  {showPassword.confirm ? <EyeOff size={16} /> : <Eye size={16} />}
                                </button>
                              </div>
                            </div>

                            <div className="flex gap-3 pt-4">
                              <motion.button
                                onClick={handlePasswordChange}
                                disabled={isUpdating}
                                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2 disabled:opacity-50"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                {isUpdating ? (
                                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                ) : (
                                  <Save size={18} />
                                )}
                                {isUpdating ? 'Changing...' : 'Change Password'}
                              </motion.button>
                              <motion.button
                                onClick={() => setEditMode(null)}
                                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-xl transition-colors duration-200"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                Cancel
                              </motion.button>
                            </div>
                          </motion.div>
                        ) : (
                          <motion.div
                            key="security-info"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="space-y-4"
                          >
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                              <Mail size={16} className="text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Email Address</p>
                                <p className="font-medium">{user?.email}</p>
                              </div>
                            </div>

                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                              <Shield size={16} className="text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Password</p>
                                <p className="font-medium">••••••••••</p>
                              </div>
                            </div>

                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                              <p className="text-blue-800 text-sm">
                                <strong>Security Tip:</strong> Use a strong password with at least 8 characters, including uppercase, lowercase, numbers, and symbols.
                              </p>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Ultra Enhanced Statistics Tab */}
            {activeTab === 'stats' && (
              <motion.div
                key="stats"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
                className="space-y-8"
              >
                {/* Statistics Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    {
                      title: 'Total Orders',
                      value: userStats.totalOrders,
                      change: '+12%',
                      icon: Package,
                      color: 'from-blue-500 to-blue-600',
                      bgColor: 'bg-blue-50',
                      trend: 'up'
                    },
                    {
                      title: 'Money Spent',
                      value: `$${userStats.totalSpent}`,
                      change: '+8%',
                      icon: CreditCard,
                      color: 'from-green-500 to-green-600',
                      bgColor: 'bg-green-50',
                      trend: 'up'
                    },
                    {
                      title: 'Loyalty Points',
                      value: userStats.loyaltyPoints,
                      change: '+25%',
                      icon: Star,
                      color: 'from-yellow-500 to-yellow-600',
                      bgColor: 'bg-yellow-50',
                      trend: 'up'
                    },
                    {
                      title: 'Average Rating',
                      value: userStats.averageRating,
                      change: '+0.2',
                      icon: Heart,
                      color: 'from-red-500 to-red-600',
                      bgColor: 'bg-red-50',
                      trend: 'up'
                    }
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.title}
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ delay: index * 0.1, type: "spring" }}
                      whileHover={{ scale: 1.05, y: -5 }}
                      className="relative overflow-hidden"
                    >
                      <div className={`${stat.bgColor} rounded-3xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 relative`}>
                        {/* Background decoration */}
                        <div className="absolute top-0 right-0 w-20 h-20 bg-white/20 rounded-full -translate-y-10 translate-x-10"></div>

                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg`}>
                              <stat.icon size={24} className="text-white" />
                            </div>
                            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${
                              stat.trend === 'up' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                            }`}>
                              {stat.trend === 'up' ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
                              {stat.change}
                            </div>
                          </div>
                          <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                          <div className="text-gray-600 font-medium">{stat.title}</div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Enhanced Activity Chart */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20"
                >
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                      <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center shadow-lg">
                        <Activity size={24} className="text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Activity Overview</h3>
                        <p className="text-gray-600">Your delivery patterns this month</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {['Week', 'Month', 'Year'].map((period) => (
                        <button
                          key={period}
                          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-xl text-sm font-medium transition-colors"
                        >
                          {period}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Simulated Chart Area */}
                  <div className="grid grid-cols-7 gap-2 mb-6">
                    {Array.from({ length: 7 }, (_, i) => (
                      <div key={i} className="text-center">
                        <div className="text-xs text-gray-500 mb-2">
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i]}
                        </div>
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: `${Math.random() * 100 + 20}px` }}
                          transition={{ delay: 0.7 + i * 0.1, duration: 0.8 }}
                          className="bg-gradient-to-t from-primary-500 to-secondary-500 rounded-lg mx-auto"
                          style={{ width: '40px' }}
                        />
                        <div className="text-xs text-gray-700 mt-2 font-medium">
                          {Math.floor(Math.random() * 10 + 1)}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      { label: 'Peak Day', value: 'Friday', icon: Calendar },
                      { label: 'Avg Orders/Week', value: userStats.weeklyOrders, icon: BarChart3 },
                      { label: 'Favorite Time', value: '2-4 PM', icon: Clock }
                    ].map((insight, index) => (
                      <motion.div
                        key={insight.label}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1 + index * 0.1 }}
                        className="bg-gray-50 rounded-2xl p-4 text-center"
                      >
                        <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                          <insight.icon size={18} className="text-primary-600" />
                        </div>
                        <div className="text-lg font-bold text-gray-900">{insight.value}</div>
                        <div className="text-sm text-gray-600">{insight.label}</div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </motion.div>
            )}

            {/* Ultra Enhanced Achievements Tab */}
            {activeTab === 'achievements' && (
              <motion.div
                key="achievements"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
                className="space-y-8"
              >
                {/* Achievements Header */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center"
                >
                  <div className="flex items-center justify-center gap-4 mb-6">
                    <motion.div
                      className="w-20 h-20 rounded-3xl bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center shadow-2xl"
                      animate={{ rotate: [0, 5, -5, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Trophy size={32} className="text-white" />
                    </motion.div>
                  </div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-yellow-600 via-yellow-500 to-yellow-600 bg-clip-text text-transparent mb-4">
                    Your Achievements
                  </h2>
                  <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                    Unlock badges and rewards as you explore our platform. You've earned {achievements.filter(a => a.earned).length} out of {achievements.length} achievements!
                  </p>
                </motion.div>

                {/* Achievement Progress */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 border border-primary-200/50"
                >
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-gray-900">Progress Overview</h3>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-primary-600">
                        {Math.round((achievements.filter(a => a.earned).length / achievements.length) * 100)}%
                      </div>
                      <div className="text-gray-600">Complete</div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${(achievements.filter(a => a.earned).length / achievements.length) * 100}%` }}
                      transition={{ delay: 0.5, duration: 1.5, ease: "easeOut" }}
                    />
                  </div>
                </motion.div>

                {/* Achievements Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {achievements.map((achievement, index) => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, y: 20, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ delay: 0.3 + index * 0.1, type: "spring" }}
                      whileHover={{ scale: 1.05, y: -5 }}
                      onClick={() => setSelectedAchievement(achievement)}
                      className="cursor-pointer group"
                    >
                      <div className={`relative rounded-3xl p-6 border-2 transition-all duration-300 overflow-hidden ${
                        achievement.earned
                          ? `bg-gradient-to-br ${achievement.color} text-white border-transparent shadow-xl hover:shadow-2xl`
                          : 'bg-gray-100 border-gray-200 hover:border-gray-300 text-gray-500'
                      }`}>
                        {/* Background decoration */}
                        {achievement.earned && (
                          <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        )}

                        {/* Rarity indicator */}
                        <div className={`absolute top-4 right-4 px-2 py-1 rounded-full text-xs font-bold ${
                          achievement.rarity === 'legendary' ? 'bg-yellow-400 text-yellow-900' :
                          achievement.rarity === 'epic' ? 'bg-purple-400 text-purple-900' :
                          achievement.rarity === 'rare' ? 'bg-blue-400 text-blue-900' :
                          'bg-gray-400 text-gray-900'
                        }`}>
                          {achievement.rarity.toUpperCase()}
                        </div>

                        <div className="relative z-10">
                          {/* Icon */}
                          <motion.div
                            className={`w-16 h-16 rounded-2xl flex items-center justify-center mb-4 ${
                              achievement.earned ? 'bg-white/20' : 'bg-gray-200'
                            }`}
                            whileHover={{ rotate: achievement.earned ? 360 : 0 }}
                            transition={{ duration: 0.6 }}
                          >
                            <achievement.icon size={28} className={achievement.earned ? 'text-white' : 'text-gray-400'} />
                          </motion.div>

                          {/* Content */}
                          <h4 className={`text-xl font-bold mb-2 ${achievement.earned ? 'text-white' : 'text-gray-700'}`}>
                            {achievement.name}
                          </h4>
                          <p className={`text-sm mb-4 ${achievement.earned ? 'text-white/80' : 'text-gray-500'}`}>
                            {achievement.description}
                          </p>

                          {/* Date or Progress */}
                          {achievement.earned ? (
                            <div className="flex items-center gap-2 text-white/70 text-xs">
                              <CheckCircle size={14} />
                              <span>Earned {new Date(achievement.date!).toLocaleDateString()}</span>
                            </div>
                          ) : (
                            <div className="text-gray-400 text-xs">
                              🔒 Not yet unlocked
                            </div>
                          )}

                          {/* Special effects for legendary achievements */}
                          {achievement.earned && achievement.rarity === 'legendary' && (
                            <motion.div
                              animate={{ opacity: [0.5, 1, 0.5] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-transparent to-yellow-400/20 rounded-3xl"
                            />
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Next Achievement Preview */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20"
                >
                  <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                    <Target size={24} className="text-primary-600" />
                    Next Achievement
                  </h3>
                  {(() => {
                    const nextAchievement = achievements.find(a => !a.earned);
                    return nextAchievement ? (
                      <div className="flex items-center gap-6">
                        <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center">
                          <nextAchievement.icon size={28} className="text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-xl font-bold text-gray-900 mb-1">{nextAchievement.name}</h4>
                          <p className="text-gray-600 mb-2">{nextAchievement.description}</p>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full h-2" style={{ width: '60%' }}></div>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">60% progress</p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Crown size={48} className="text-yellow-500 mx-auto mb-4" />
                        <h4 className="text-xl font-bold text-gray-900 mb-2">All Achievements Unlocked!</h4>
                        <p className="text-gray-600">Congratulations! You've earned every achievement available.</p>
                      </div>
                    );
                  })()}
                </motion.div>
              </motion.div>
            )}

            {/* Ultra Enhanced Settings Tab */}
            {activeTab === 'settings' && (
              <motion.div
                key="settings"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
                className="space-y-8"
              >
                {/* Settings Categories */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Notifications Settings */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20"
                  >
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                        <Bell size={24} className="text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Notifications</h3>
                        <p className="text-gray-600">Manage your notification preferences</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {[
                        { label: 'Push Notifications', desc: 'Receive notifications on your device', enabled: notificationsEnabled },
                        { label: 'Email Updates', desc: 'Get updates via email', enabled: true },
                        { label: 'Order Updates', desc: 'Notifications about your orders', enabled: true },
                        { label: 'Promotional Offers', desc: 'Special deals and discounts', enabled: false }
                      ].map((setting, index) => (
                        <motion.div
                          key={setting.label}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2 + index * 0.1 }}
                          className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors"
                        >
                          <div>
                            <div className="font-semibold text-gray-900">{setting.label}</div>
                            <div className="text-sm text-gray-600">{setting.desc}</div>
                          </div>
                          <motion.button
                            className={`w-12 h-6 rounded-full transition-colors duration-300 ${
                              setting.enabled ? 'bg-primary-500' : 'bg-gray-300'
                            }`}
                            onClick={() => {
                              if (setting.label === 'Push Notifications') {
                                setNotificationsEnabled(!notificationsEnabled);
                              }
                            }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <motion.div
                              className="w-5 h-5 bg-white rounded-full shadow-sm"
                              animate={{ x: setting.enabled ? 26 : 2 }}
                              transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            />
                          </motion.button>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Appearance Settings */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20"
                  >
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center shadow-lg">
                        <Palette size={24} className="text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">Appearance</h3>
                        <p className="text-gray-600">Customize your experience</p>
                      </div>
                    </div>

                    <div className="space-y-6">
                      {/* Theme Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">Theme</label>
                        <div className="grid grid-cols-2 gap-3">
                          {[
                            { name: 'Light', icon: Sun, active: !isDarkMode },
                            { name: 'Dark', icon: Moon, active: isDarkMode }
                          ].map((theme) => (
                            <motion.button
                              key={theme.name}
                              onClick={() => setIsDarkMode(theme.name === 'Dark')}
                              className={`p-4 rounded-2xl border-2 transition-all duration-300 ${
                                theme.active
                                  ? 'border-primary-500 bg-primary-50'
                                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                              }`}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <theme.icon size={24} className={`mx-auto mb-2 ${
                                theme.active ? 'text-primary-600' : 'text-gray-400'
                              }`} />
                              <div className={`font-medium ${
                                theme.active ? 'text-primary-600' : 'text-gray-600'
                              }`}>
                                {theme.name}
                              </div>
                            </motion.button>
                          ))}
                        </div>
                      </div>

                      {/* Sound Settings */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">Sound</label>
                        <motion.button
                          onClick={() => setSoundEnabled(!soundEnabled)}
                          className={`w-full p-4 rounded-2xl border-2 transition-all duration-300 ${
                            soundEnabled
                              ? 'border-green-500 bg-green-50'
                              : 'border-gray-200 bg-gray-50'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-center justify-center gap-3">
                            {soundEnabled ? (
                              <Volume2 size={24} className="text-green-600" />
                            ) : (
                              <VolumeX size={24} className="text-gray-400" />
                            )}
                            <span className={`font-medium ${
                              soundEnabled ? 'text-green-600' : 'text-gray-600'
                            }`}>
                              {soundEnabled ? 'Sound On' : 'Sound Off'}
                            </span>
                          </div>
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Account Management */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-white/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20"
                >
                  <div className="flex items-center gap-4 mb-8">
                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-lg">
                      <Shield size={24} className="text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">Account Management</h3>
                      <p className="text-gray-600">Manage your account and data</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[
                      { icon: Download, label: 'Export Data', desc: 'Download your data', color: 'from-blue-500 to-blue-600' },
                      { icon: FileText, label: 'Privacy Policy', desc: 'View our privacy policy', color: 'from-green-500 to-green-600' },
                      { icon: HelpCircle, label: 'Help Center', desc: 'Get help and support', color: 'from-purple-500 to-purple-600' },
                      { icon: MessageSquare, label: 'Feedback', desc: 'Send us feedback', color: 'from-orange-500 to-orange-600' }
                    ].map((action, index) => (
                      <motion.button
                        key={action.label}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 + index * 0.1, type: "spring" }}
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className="group text-left"
                      >
                        <div className={`p-6 bg-gradient-to-r ${action.color} text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300`}>
                          <action.icon size={24} className="mb-3 group-hover:scale-110 transition-transform duration-300" />
                          <div className="font-semibold mb-1">{action.label}</div>
                          <div className="text-white/80 text-sm">{action.desc}</div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

        {/* Ultra Enhanced Logout Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="mt-16"
              >
                <div className="bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-3xl p-12 text-white shadow-2xl relative overflow-hidden">
                  {/* Ultra Enhanced Background Effects */}
                  <div className="absolute inset-0 overflow-hidden">
                    <motion.div
                      className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full"
                      animate={{
                        scale: [1, 1.3, 1],
                        rotate: [0, 180, 360],
                        opacity: [0.1, 0.3, 0.1]
                      }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      style={{ transform: 'translate(50%, -50%)' }}
                    />
                    <motion.div
                      className="absolute bottom-0 left-0 w-32 h-32 bg-white/15 rounded-full"
                      animate={{
                        scale: [1.2, 0.8, 1.2],
                        rotate: [360, 0],
                        opacity: [0.2, 0.4, 0.2]
                      }}
                      transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                      style={{ transform: 'translate(-50%, 50%)' }}
                    />

                    {/* Floating particles */}
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-2 h-2 bg-white/30 rounded-full"
                        style={{
                          left: `${20 + i * 12}%`,
                          top: `${30 + (i % 2) * 40}%`,
                        }}
                        animate={{
                          y: [0, -15, 0],
                          opacity: [0.3, 0.8, 0.3],
                          scale: [1, 1.3, 1],
                        }}
                        transition={{
                          duration: 3 + i * 0.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: i * 0.3,
                        }}
                      />
                    ))}
                  </div>

                  <div className="relative z-10 text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.8, type: "spring", stiffness: 200 }}
                      className="w-24 h-24 mx-auto mb-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center"
                    >
                      <LogOut size={36} />
                    </motion.div>

                    <motion.h3
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1 }}
                      className="text-4xl font-bold mb-4"
                    >
                      Ready to Sign Out?
                    </motion.h3>

                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1.1 }}
                      className="text-red-100 mb-8 text-xl max-w-md mx-auto"
                    >
                      Thanks for using Wasel! You can always sign back in anytime to continue your journey.
                    </motion.p>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1.2 }}
                      className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                    >
                      <motion.button
                        onClick={handleLogout}
                        className="px-12 py-4 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-2xl font-semibold transition-all duration-300 flex items-center gap-4 text-lg shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05, y: -3 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <LogOut size={24} />
                        <span>Sign Out</span>
                      </motion.button>

                      <motion.button
                        onClick={() => window.history.back()}
                        className="px-8 py-4 bg-transparent hover:bg-white/10 border border-white/50 rounded-2xl font-medium transition-all duration-300 text-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Stay Logged In
                      </motion.button>
                    </motion.div>

                    {/* User appreciation message */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1.5 }}
                      className="mt-8 p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20"
                    >
                      <div className="flex items-center justify-center gap-3 mb-2">
                        <Heart size={20} className="text-red-200" />
                        <span className="font-semibold text-red-100">Thank you for being awesome!</span>
                        <Heart size={20} className="text-red-200" />
                      </div>
                      <p className="text-red-200 text-sm">
                        You've completed {userStats.totalOrders} orders and earned {userStats.loyaltyPoints} loyalty points. Amazing!
                      </p>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Ultra Enhanced Floating Action Buttons */}
        <div className="fixed bottom-8 right-8 z-50 flex flex-col gap-3">
          {/* Share Profile FAB */}
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 2, type: "spring", stiffness: 200 }}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="group relative"
          >
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden">
              {/* Glow effect */}
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5]
                }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 bg-blue-400 rounded-full blur-md"
              />

              {/* Icon */}
              <Share2 className="text-white relative z-10 group-hover:scale-110 transition-transform duration-200" size={22} />

              {/* Ripple effect on hover */}
              <motion.div
                className="absolute inset-0 bg-white/20 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"
              />
            </div>
          </motion.button>

          {/* Settings FAB */}
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 2.2, type: "spring", stiffness: 200 }}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setActiveTab('settings')}
            className="group relative"
          >
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden">
              {/* Glow effect */}
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5]
                }}
                transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                className="absolute inset-0 bg-purple-400 rounded-full blur-md"
              />

              {/* Icon */}
              <Settings className="text-white relative z-10 group-hover:rotate-90 transition-transform duration-300" size={22} />

              {/* Ripple effect on hover */}
              <motion.div
                className="absolute inset-0 bg-white/20 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"
              />
            </div>
          </motion.button>

          {/* Achievements FAB */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 2.4, type: "spring", stiffness: 200 }}
            className="relative"
          >
            <motion.button
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveTab('achievements')}
              className="group relative"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden">
                {/* Glow effect */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                  className="absolute inset-0 bg-yellow-400 rounded-full blur-md"
                />

                {/* Icon */}
                <Trophy className="text-white relative z-10 group-hover:scale-110 transition-transform duration-200" size={22} />

                {/* Ripple effect on hover */}
                <motion.div
                  className="absolute inset-0 bg-white/20 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"
                />
              </div>
            </motion.button>

            {/* Achievement notification badge - positioned outside button */}
            {achievements.filter(a => a.earned).length > 5 && (
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg border-2 border-white z-10"
              >
                {achievements.filter(a => a.earned).length}
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Ultra Enhanced Achievement Modal */}
        <AnimatePresence>
          {selectedAchievement && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setSelectedAchievement(null)}
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.8, opacity: 0, y: 20 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl relative overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Background decoration */}
                <div className={`absolute inset-0 bg-gradient-to-br ${selectedAchievement.color} opacity-10`} />

                <div className="relative z-10">
                  <div className="text-center mb-6">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className={`w-20 h-20 mx-auto mb-4 rounded-3xl bg-gradient-to-br ${selectedAchievement.color} flex items-center justify-center shadow-xl`}
                    >
                      <selectedAchievement.icon size={32} className="text-white" />
                    </motion.div>

                    <motion.h3
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-2xl font-bold text-gray-900 mb-2"
                    >
                      {selectedAchievement.name}
                    </motion.h3>

                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-gray-600 mb-4"
                    >
                      {selectedAchievement.description}
                    </motion.p>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.5 }}
                      className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-bold ${
                        selectedAchievement.rarity === 'legendary' ? 'bg-yellow-100 text-yellow-800' :
                        selectedAchievement.rarity === 'epic' ? 'bg-purple-100 text-purple-800' :
                        selectedAchievement.rarity === 'rare' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <Gem size={14} />
                      {selectedAchievement.rarity.toUpperCase()}
                    </motion.div>
                  </div>

                  {selectedAchievement.earned ? (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="text-center"
                    >
                      <div className="flex items-center justify-center gap-2 text-green-600 mb-2">
                        <CheckCircle size={20} />
                        <span className="font-semibold">Achievement Unlocked!</span>
                      </div>
                      <p className="text-gray-500 text-sm">
                        Earned on {new Date(selectedAchievement.date!).toLocaleDateString()}
                      </p>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="text-center"
                    >
                      <div className="flex items-center justify-center gap-2 text-gray-400 mb-2">
                        <X size={20} />
                        <span className="font-semibold">Not Yet Unlocked</span>
                      </div>
                      <p className="text-gray-500 text-sm">
                        Keep using the app to unlock this achievement!
                      </p>
                    </motion.div>
                  )}

                  <motion.button
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                    onClick={() => setSelectedAchievement(null)}
                    className="w-full mt-6 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-2xl font-semibold hover:shadow-lg transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Close
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success Toast for Actions */}
        <AnimatePresence>
          {isUpdating && (
            <motion.div
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 50, scale: 0.9 }}
              className="fixed bottom-8 left-8 bg-white rounded-2xl p-4 shadow-2xl border border-gray-200 z-50"
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-6 h-6 border-2 border-primary-200 border-t-primary-600 rounded-full"
                />
                <span className="font-medium text-gray-900">Updating your profile...</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ProfilePage;