import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, YStack, XStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useSetSendPackage } from './useSendPackageStore';
import { useUpdateRequestPickup } from './useRequestPickupStore';
import { useSetOrderAddress } from '../useLastOrderStore';
import { Dimensions, Platform } from 'react-native';
import MapView, { Marker } from 'react-native-maps';

export default function SelectLocation() {
  const router = useRouter();
  const { type } = useLocalSearchParams<{ type: 'pickup' | 'dropoff' | 'request' | 'orderaddress' }>();
  const setPickup = useSetSendPackage((s) => s.setPickup);
  const setDropoff = useSetSendPackage((s) => s.setDropoff);
  const updateField = useUpdateRequestPickup((r) => r.updateField);
  const setOrderAddress = useSetOrderAddress((o) => o.setAddress);

  console.log('🗺️ SelectLocation component loaded');
  console.log('🗺️ Type parameter:', type);

  const [marker, setMarker] = useState<[number, number] | null>(null); // [lng, lat]
  const [region, setRegion] = useState<[number, number] | null>(null);
  const [address, setAddress] = useState<string | null>(null);
  const [addressLoading, setAddressLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [mapError, setMapError] = useState(false);
  const [fallbackMode, setFallbackMode] = useState(false);
  const { width, height } = Dimensions.get('window');

  useEffect(() => {
    console.log('🗺️ Starting location setup...');
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        console.log('🗺️ Location permission status:', status);

        if (status !== 'granted') {
          console.log('🗺️ Location permission denied. Using default Nablus location.');
          alert('Location permission denied. Defaulting to Nablus.');
          const lngLat: [number, number] = [35.2544, 32.2211];
          setRegion(lngLat);
          setMarker(lngLat);
          reverseGeocode(lngLat[1], lngLat[0]);
          setLoading(false);
          return;
        }

        console.log('🗺️ Getting current position...');
        const loc = await Location.getCurrentPositionAsync({});
        console.log('🗺️ Current location:', loc.coords);
        const lngLat: [number, number] = [loc.coords.longitude, loc.coords.latitude];
        setRegion(lngLat);
        setMarker(lngLat);
        reverseGeocode(loc.coords.latitude, loc.coords.longitude);
        setLoading(false);
        console.log('🗺️ Location setup complete');
      } catch (error) {
        console.error('🗺️ Error setting up location:', error);
        // Fallback to Nablus
        const lngLat: [number, number] = [35.2544, 32.2211];
        setRegion(lngLat);
        setMarker(lngLat);
        setLoading(false);
      }
    })();
  }, []);

  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      setAddressLoading(true);
      const geo = await Location.reverseGeocodeAsync({ latitude: lat, longitude: lng });
      if (!geo || geo.length === 0) {
        setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
        return;
      }
      const g = geo[0];
      const formatted = `${g.name || ''}, ${g.city || ''}, ${g.region || ''}`.trim();
      setAddress(formatted || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } catch (e) {
      setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } finally {
      setAddressLoading(false);
    }
  };

  const onMapPress = (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;
    const coords: [number, number] = [longitude, latitude];
    setMarker(coords);
    reverseGeocode(latitude, longitude);
  };

  const handleSelect = () => {
    if (!marker) return;
    const [lng, lat] = marker;

    const locationData = {
      lat,
      lng,
      address: address || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`,
    };

    if (type === 'pickup') setPickup(locationData);
    else if(type === 'dropoff') setDropoff(locationData);
    else if(type === 'request') updateField('pickup', locationData);
    else setOrderAddress(locationData);

    router.back();
  };

  if (loading || !region) {
    console.log('🗺️ Still loading... loading:', loading, 'region:', region);
    return (
      <View flex={1} ai="center" jc="center">
        <Spinner size="large" color="$primary" />
        <Text mt="$3">Fetching location...</Text>
        <Text mt="$2" fontSize="$3" color="$gray10">
          Loading: {loading ? 'true' : 'false'}, Region: {region ? 'set' : 'null'}
        </Text>
      </View>
    );
  }

  console.log('🗺️ Rendering map with region:', region);

  // Quick location selection mode
  if (fallbackMode) {
    return (
      <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
        <YStack flex={1} p="$4" gap="$4" jc="center">
          <YStack ai="center" gap="$3" mb="$4">
            <View style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: '#7c3aed',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Ionicons name="location" size={40} color="white" />
            </View>
            <Text fontSize="$6" fontWeight="bold" textAlign="center" color="$gray12">
              Quick Location Selection
            </Text>
            <Text fontSize="$4" textAlign="center" color="$gray10" px="$2">
              Choose from popular locations or return to map
            </Text>
          </YStack>

          <YStack gap="$3">
            <Button
              size="$5"
              bg="$blue10"
              color="white"
              icon={<Ionicons name="location" size={20} color="white" />}
              onPress={() => {
                const nablus = {
                  lat: 32.2211,
                  lng: 35.2544,
                  address: "Nablus, Palestine"
                };
                if (type === 'pickup') setPickup(nablus);
                else if(type === 'dropoff') setDropoff(nablus);
                else if(type === 'request') updateField('pickup', nablus);
                else setOrderAddress(nablus);
                router.back();
              }}
              borderRadius="$4"
            >
              Nablus, Palestine
            </Button>

            <Button
              size="$5"
              bg="$green10"
              color="white"
              icon={<Ionicons name="location" size={20} color="white" />}
              onPress={() => {
                const ramallah = {
                  lat: 31.9073,
                  lng: 35.2044,
                  address: "Ramallah, Palestine"
                };
                if (type === 'pickup') setPickup(ramallah);
                else if(type === 'dropoff') setDropoff(ramallah);
                else if(type === 'request') updateField('pickup', ramallah);
                else setOrderAddress(ramallah);
                router.back();
              }}
              borderRadius="$4"
            >
              Ramallah, Palestine
            </Button>

            <Button
              size="$5"
              bg="$purple10"
              color="white"
              icon={<Ionicons name="location" size={20} color="white" />}
              onPress={() => {
                const jerusalem = {
                  lat: 31.7683,
                  lng: 35.2137,
                  address: "Jerusalem, Palestine"
                };
                if (type === 'pickup') setPickup(jerusalem);
                else if(type === 'dropoff') setDropoff(jerusalem);
                else if(type === 'request') updateField('pickup', jerusalem);
                else setOrderAddress(jerusalem);
                router.back();
              }}
              borderRadius="$4"
            >
              Jerusalem, Palestine
            </Button>

            <XStack gap="$2" mt="$2">
              <Button
                size="$4"
                bg="$gray8"
                color="white"
                icon={<Ionicons name="map" size={16} color="white" />}
                onPress={() => setFallbackMode(false)}
                flex={1}
                borderRadius="$4"
              >
                Back to Map
              </Button>

              <Button
                size="$4"
                bg="$red10"
                color="white"
                icon={<Ionicons name="close" size={16} color="white" />}
                onPress={() => router.back()}
                flex={1}
                borderRadius="$4"
              >
                Cancel
              </Button>
            </XStack>
          </YStack>
        </YStack>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f5f5f5', position: 'relative' }}>
      {/* Location Info Header */}
      <View style={{
        position: 'absolute',
        top: 60,
        left: 15,
        right: 15,
        zIndex: 1000,
        backgroundColor: 'rgba(255,255,255,0.95)',
        padding: 15,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3
      }}>
        <Text fontSize="$4" fontWeight="600" color="$gray12" textAlign="center">
          📍 Select Your Location
        </Text>
        {address && (
          <Text fontSize="$3" color="$gray10" textAlign="center" mt="$1">
            Current: {address}
          </Text>
        )}
        <XStack gap="$2" mt="$2" jc="center">
          <Button
            size="$2"
            bg="$blue10"
            color="white"
            onPress={() => setFallbackMode(true)}
            flex={1}
          >
            📋 Quick Select
          </Button>
        </XStack>
      </View>

      {/* Real Map for Location Selection */}
      <View style={{ flex: 1, width: '100%', height: '100%' }}>
        <MapView
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: width,
            height: height,
            backgroundColor: '#f0f0f0'
          }}
        initialRegion={{
          latitude: region[1],
          longitude: region[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        region={{
          latitude: region[1],
          longitude: region[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        onPress={onMapPress}
        showsUserLocation={true}
        showsMyLocationButton={true}
        showsCompass={true}
        showsScale={true}
        mapType="standard"
        loadingEnabled={true}
        loadingIndicatorColor="#7c3aed"
        loadingBackgroundColor="#f5f5f5"
        onMapReady={() => {
          console.log('🗺️ Map is ready!');
          // Force a small region change to trigger rendering
          setTimeout(() => {
            setRegion([region[0], region[1]]);
          }, 100);
        }}
        onRegionChange={(newRegion) => {
          console.log('🗺️ Region changed:', newRegion);
        }}
        onError={(error) => {
          console.error('🗺️ Map error:', error);
          setMapError(true);
        }}
        onMapLoaded={() => {
          console.log('🗺️ Map loaded successfully');
        }}
      >
        {/* Selected Marker */}
        {marker && (
          <Marker
            coordinate={{
              latitude: marker[1],
              longitude: marker[0],
            }}
            pinColor="purple"
          />
        )}
      </MapView>
      </View>

      <YStack
        position="absolute"
        bottom={30}
        width="100%"
        px="$4"
        gap="$3"
        zIndex={10}
        pointerEvents="box-none"
      >
        {addressLoading ? (
          <Text fontSize="$5" textAlign="center" color="$gray10">
            Fetching address...
          </Text>
        ) : address ? (
          <Text fontSize="$5" textAlign="center" color="$primary">
            {address}
          </Text>
        ) : null}

        <Button
          size="$5"
          bg="$primary"
          color="white"
          icon={<Ionicons name="checkmark-circle" size={24} color="white" />}
          disabled={!marker}
          onPress={() => {
            console.log('🗺️ Confirm Location clicked');
            handleSelect();
          }}
          hoverStyle={{ bg: '$third' }}
          pressStyle={{ bg: '$third' }}
          borderRadius="$4"
          fontWeight="600"
        >
          Confirm Location
        </Button>

        <XStack gap="$2">
          <Button
            size="$4"
            bg="$gray8"
            color="white"
            icon={<Ionicons name="list" size={16} color="white" />}
            onPress={() => setFallbackMode(true)}
            flex={1}
            borderRadius="$4"
          >
            Quick Select
          </Button>

          <Button
            size="$4"
            bg="$red10"
            color="white"
            icon={<Ionicons name="close" size={16} color="white" />}
            onPress={() => {
              console.log('🗺️ Cancel button clicked - going back');
              router.back();
            }}
            flex={1}
            borderRadius="$4"
          >
            Cancel
          </Button>
        </XStack>
      </YStack>
    </View>
  );
}
