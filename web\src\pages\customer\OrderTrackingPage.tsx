import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  ArrowLeft, Clock, MapPin, Phone, User, Package, CheckCircle, Truck,
  Star, Heart, Share2, Navigation, Zap, Sparkles, Crown, Shield,
  MessageCircle, Eye, RotateCcw, Timer, Award, TrendingUp, X
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOrdersStore } from '../../stores/ordersStore';
import { MapContainer, TileLayer, Marker, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const OrderTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { getOrderById } = useOrdersStore();
  const orderId = searchParams.get('orderId');

  // Get order from store
  const order = orderId ? getOrderById(orderId) : null;

  // Enhanced state management
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [activeTab, setActiveTab] = useState<'timeline' | 'details' | 'driver'>('timeline');
  const [isMapExpanded, setIsMapExpanded] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  // Handle scroll for header animation with debouncing (matching home page pattern)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 300);
      }, 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  if (!order) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-emerald-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-md"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              className="mb-8"
            >
              <Package className="w-24 h-24 text-purple-400 mx-auto" />
            </motion.div>
            <h2 className="text-4xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-4">
              Order Not Found
            </h2>
            <p className="text-white/70 mb-8 text-lg">
              The order you're looking for doesn't exist or has been removed.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/customer/orders')}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-8 rounded-xl font-semibold shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300"
            >
              Back to Orders
            </motion.button>
          </motion.div>
        </div>
      </div>
    );
  }

  // Mock location data for tracking
  const trackingData = {
    supplierLocation: { lat: 32.2211, lng: 35.2544 },
    deliveryLocation: { lat: 32.2156, lng: 35.2631 },
    driverLocation: { lat: 32.2180, lng: 35.2580 }, // Between supplier and customer
  };

  // Order status timeline
  const statusTimeline = [
    { 
      status: 'Order Placed', 
      time: '10:30 AM', 
      completed: true, 
      description: 'Your order has been placed successfully' 
    },
    { 
      status: 'Order Confirmed', 
      time: '10:32 AM', 
      completed: true, 
      description: 'Restaurant confirmed your order' 
    },
    { 
      status: 'Preparing', 
      time: '10:35 AM', 
      completed: true, 
      description: 'Your food is being prepared' 
    },
    { 
      status: 'Out for Delivery', 
      time: '11:00 AM', 
      completed: true, 
      description: 'Driver is on the way to deliver your order' 
    },
    { 
      status: 'Delivered', 
      time: 'ETA 11:15 AM', 
      completed: false, 
      description: 'Order will be delivered to your address' 
    }
  ];

  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCallDriver = () => {
    window.open(`tel:${order.driverPhone}`);
  };

  const handleViewOrderDetails = () => {
    navigate(`/customer/order-details?orderId=${order.id}`);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-emerald-500/30 to-cyan-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-orange-500/30 to-pink-600/30 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100, -20],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Sticky Header with Scroll Animation */}
      <motion.div
        className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
        animate={{
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Compact Header Content */}
            <motion.div
              animate={{
                opacity: isHeaderCompact ? 1 : 0,
                height: isHeaderCompact ? "auto" : 0,
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate(-1)}
                    className="p-2 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:bg-white/20 transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                  <div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                      Track Order
                    </h1>
                    <p className="text-white/60 text-sm">Order #{order.id}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowShareModal(true)}
                    className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl"
                  >
                    <Share2 className="text-white" size={18} />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleCallDriver}
                    className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl"
                  >
                    <Phone className="text-white" size={18} />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <div className="relative z-10 pt-32 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <motion.div
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
              className="mb-6"
            >
              <div className="relative inline-block">
                <Truck className="w-16 h-16 text-purple-400 mx-auto" />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute -inset-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-full blur-xl"
                />
              </div>
            </motion.div>
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-4">
              Track Your Order
            </h1>
            <p className="text-white/70 text-xl mb-2">Order #{order.id}</p>
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full border border-green-400/30"
            >
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-300 font-medium">{order.status}</span>
            </motion.div>
          </motion.div>

          {/* Enhanced Grid Layout */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Left Column - Order Status & Timeline */}
            <div className="xl:col-span-2 space-y-6">
              {/* Current Status Card */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg shadow-green-500/25"
                    >
                      <Truck className="w-8 h-8 text-white" />
                    </motion.div>
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-1">{order.status}</h2>
                      <p className="text-white/70 text-lg">ETA: {order.estimatedTime}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setIsMapExpanded(!isMapExpanded)}
                      className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg"
                    >
                      <Eye className="text-white" size={20} />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg"
                    >
                      <RotateCcw className="text-white" size={20} />
                    </motion.button>
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-xl p-6"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <Sparkles className="w-6 h-6 text-green-300" />
                    <h3 className="text-white font-semibold text-lg">Live Update</h3>
                  </div>
                  <p className="text-green-200 font-medium text-lg">
                    🚚 Your order is on the way! {order.driverName} will deliver it soon.
                  </p>
                  <div className="mt-4 flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Timer className="w-4 h-4 text-green-300" />
                      <span className="text-green-200 text-sm">Real-time tracking</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-green-300" />
                      <span className="text-green-200 text-sm">Secure delivery</span>
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Driver Information Card */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white flex items-center gap-3">
                    <Crown className="w-7 h-7 text-yellow-400" />
                    Your Driver
                  </h3>
                  <div className="flex items-center gap-2">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <span className="text-white font-semibold">4.9</span>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="relative"
                  >
                    <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/25">
                      <User className="w-10 h-10 text-white" />
                    </div>
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute -inset-1 bg-gradient-to-r from-purple-500/30 to-blue-500/30 rounded-full blur-md"
                    />
                  </motion.div>

                  <div className="flex-1">
                    <h4 className="text-xl font-bold text-white mb-2">{order.driverName}</h4>
                    <p className="text-white/70 mb-3">{order.driverPhone}</p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Award className="w-4 h-4 text-green-400" />
                        <span className="text-green-300 text-sm font-medium">Verified Driver</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-blue-400" />
                        <span className="text-blue-300 text-sm font-medium">500+ Deliveries</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleCallDriver}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold shadow-lg shadow-green-500/25 hover:shadow-green-500/40 transition-all duration-300"
                    >
                      <Phone className="w-5 h-5" />
                      Call Driver
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300"
                    >
                      <MessageCircle className="w-5 h-5" />
                      Message
                    </motion.button>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Order Timeline */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center justify-between mb-8">
                  <h3 className="text-2xl font-bold text-white flex items-center gap-3">
                    <Zap className="w-7 h-7 text-yellow-400" />
                    Order Timeline
                  </h3>
                  <div className="flex gap-2">
                    {['timeline', 'details', 'driver'].map((tab) => (
                      <motion.button
                        key={tab}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setActiveTab(tab as any)}
                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                          activeTab === tab
                            ? 'bg-gradient-to-r from-purple-500 to-blue-600 text-white shadow-lg'
                            : 'bg-white/10 text-white/70 hover:bg-white/20'
                        }`}
                      >
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}
                      </motion.button>
                    ))}
                  </div>
                </div>

                <div className="space-y-6">
                  {statusTimeline.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className="flex items-start gap-6 relative"
                    >
                      {/* Timeline Line */}
                      {index < statusTimeline.length - 1 && (
                        <div className="absolute left-6 top-12 w-0.5 h-16 bg-gradient-to-b from-purple-400 to-transparent"></div>
                      )}

                      {/* Status Icon */}
                      <motion.div
                        animate={step.completed ? { scale: [1, 1.1, 1] } : {}}
                        transition={{ duration: 2, repeat: Infinity }}
                        className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg ${
                          step.completed
                            ? 'bg-gradient-to-r from-green-400 to-emerald-500 shadow-green-500/25'
                            : 'bg-white/20 backdrop-blur-xl border border-white/30'
                        }`}
                      >
                        {step.completed ? (
                          <CheckCircle className="w-6 h-6 text-white" />
                        ) : (
                          <Clock className="w-6 h-6 text-white/70" />
                        )}
                      </motion.div>

                      {/* Status Content */}
                      <div className="flex-1 bg-white/5 backdrop-blur-xl rounded-xl p-4 border border-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className={`font-bold text-lg ${
                            step.completed ? 'text-white' : 'text-white/60'
                          }`}>
                            {step.status}
                          </h4>
                          <span className={`text-sm font-medium px-3 py-1 rounded-full ${
                            step.completed
                              ? 'bg-green-500/20 text-green-300 border border-green-400/30'
                              : 'bg-white/10 text-white/50 border border-white/20'
                          }`}>
                            {step.time}
                          </span>
                        </div>
                        <p className={`text-sm ${
                          step.completed ? 'text-white/80' : 'text-white/50'
                        }`}>
                          {step.description}
                        </p>

                        {step.completed && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.2 }}
                            className="mt-3 flex items-center gap-2"
                          >
                            <Sparkles className="w-4 h-4 text-green-300" />
                            <span className="text-green-300 text-xs font-medium">Completed</span>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Enhanced Order Summary */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white flex items-center gap-3">
                    <Package className="w-7 h-7 text-blue-400" />
                    Order Summary
                  </h3>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-gradient-to-r from-pink-500 to-red-500 rounded-xl"
                  >
                    <Heart className="w-5 h-5 text-white" />
                  </motion.button>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                    <span className="font-medium text-white flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-purple-400" />
                      From:
                    </span>
                    <span className="text-white/80 font-semibold">{order.supplier.name}</span>
                  </div>

                  <div className="space-y-3">
                    {order.items.map((item, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className="flex justify-between items-center p-3 bg-white/5 rounded-lg border border-white/10"
                      >
                        <span className="text-white/80 flex items-center gap-2">
                          <span className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            {item.qty}
                          </span>
                          {item.product.name}
                        </span>
                        <span className="text-white font-semibold">₪{(item.product.price * item.qty).toFixed(2)}</span>
                      </motion.div>
                    ))}
                  </div>

                  <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

                  <div className="flex justify-between items-center p-4 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl border border-purple-400/30">
                    <span className="text-white font-bold text-lg">Total:</span>
                    <span className="text-white font-bold text-xl">₪{order.total}</span>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleViewOrderDetails}
                  className="w-full py-4 px-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl font-semibold shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Eye className="w-5 h-5" />
                  View Full Order Details
                </motion.button>
              </motion.div>
            </div>

            {/* Right Column - Enhanced Live Map */}
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden shadow-2xl"
              >
                <div className="p-6 border-b border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-2xl font-bold text-white flex items-center gap-3">
                        <Navigation className="w-7 h-7 text-green-400" />
                        Live Tracking
                      </h3>
                      <p className="text-white/70 mt-1">Follow your driver's location in real-time</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="w-3 h-3 bg-green-400 rounded-full shadow-lg shadow-green-400/50"
                      ></motion.div>
                      <span className="text-green-300 font-medium">Live</span>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setIsMapExpanded(true)}
                        className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl"
                      >
                        <Eye className="w-5 h-5 text-white" />
                      </motion.button>
                    </div>
                  </div>
                </div>

                <div className="h-96 relative">
                  <MapContainer
                    center={[
                      order.driverLocation?.lat ?? trackingData.driverLocation.lat,
                      order.driverLocation?.lng ?? trackingData.driverLocation.lng
                    ]}
                    zoom={14}
                    style={{ height: '100%', width: '100%' }}
                  >
                    <TileLayer
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    />

                    {/* Supplier Marker */}
                    <Marker position={[trackingData.supplierLocation.lat, trackingData.supplierLocation.lng]}>
                      <Popup>
                        <div className="text-center p-2">
                          <strong className="text-lg">{order.supplier.name}</strong>
                          <br />
                          <span className="text-sm text-gray-600">🏪 Restaurant</span>
                        </div>
                      </Popup>
                    </Marker>

                    {/* Driver Marker */}
                    <Marker position={[trackingData.driverLocation.lat, trackingData.driverLocation.lng]}>
                      <Popup>
                        <div className="text-center p-2">
                          <strong className="text-lg">{order.driverName}</strong>
                          <br />
                          <span className="text-sm text-gray-600">🚚 Your Driver</span>
                          <br />
                          <span className="text-xs text-green-600 font-medium">On the way!</span>
                        </div>
                      </Popup>
                    </Marker>

                    {/* Delivery Location Marker */}
                    <Marker position={[trackingData.deliveryLocation.lat, trackingData.deliveryLocation.lng]}>
                      <Popup>
                        <div className="text-center p-2">
                          <strong className="text-lg">Delivery Address</strong>
                          <br />
                          <span className="text-sm text-gray-600">📍 {order.address}</span>
                        </div>
                      </Popup>
                    </Marker>
                  </MapContainer>

                  {/* Map Overlay Controls */}
                  <div className="absolute top-4 right-4 flex flex-col gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-3 bg-white/20 backdrop-blur-xl rounded-xl border border-white/30 text-white hover:bg-white/30 transition-colors"
                    >
                      <RotateCcw className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleCallDriver}
                      className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl text-white shadow-lg"
                    >
                      <Phone className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Map Legend */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6 shadow-2xl"
              >
                <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <MapPin className="w-6 h-6 text-purple-400" />
                  Map Legend
                </h4>
                <div className="space-y-4">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="w-4 h-4 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full shadow-lg shadow-blue-500/25"
                    ></motion.div>
                    <span className="text-white font-medium">🏪 Restaurant Location</span>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-600 rounded-full shadow-lg shadow-green-500/25"
                    ></motion.div>
                    <span className="text-white font-medium">🚚 Driver Location (Live)</span>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2.5, repeat: Infinity }}
                      className="w-4 h-4 bg-gradient-to-r from-red-400 to-pink-600 rounded-full shadow-lg shadow-red-500/25"
                    ></motion.div>
                    <span className="text-white font-medium">📍 Your Delivery Address</span>
                  </motion.div>
                </div>

                {/* Quick Actions */}
                <div className="mt-6 pt-4 border-t border-white/10">
                  <h5 className="text-white font-semibold mb-3">Quick Actions</h5>
                  <div className="grid grid-cols-2 gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowShareModal(true)}
                      className="flex items-center gap-2 p-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl border border-purple-400/30 text-white hover:bg-purple-500/30 transition-colors"
                    >
                      <Share2 className="w-4 h-4" />
                      <span className="text-sm font-medium">Share</span>
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleViewOrderDetails}
                      className="flex items-center gap-2 p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl border border-orange-400/30 text-white hover:bg-orange-500/30 transition-colors"
                    >
                      <Package className="w-4 h-4" />
                      <span className="text-sm font-medium">Details</span>
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Full Screen Map Modal */}
      <AnimatePresence>
        {isMapExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="h-full w-full bg-white/5 backdrop-blur-xl border border-white/10 relative"
            >
              {/* Full Screen Map Header */}
              <div className="absolute top-0 left-0 right-0 z-10 bg-black/50 backdrop-blur-xl border-b border-white/20 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Navigation className="w-8 h-8 text-green-400" />
                    <div>
                      <h3 className="text-2xl font-bold text-white">Live Tracking - Full View</h3>
                      <p className="text-white/70">Order #{order.id} • {order.driverName}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="w-3 h-3 bg-green-400 rounded-full shadow-lg shadow-green-400/50"
                    />
                    <span className="text-green-300 font-medium">Live</span>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setIsMapExpanded(false)}
                      className="p-3 bg-red-500/80 backdrop-blur-xl rounded-xl border border-red-400/50 text-white hover:bg-red-500 transition-colors shadow-lg"
                    >
                      <X className="w-6 h-6" />
                    </motion.button>
                  </div>
                </div>
              </div>

              {/* Full Screen Map Container */}
              <div className="h-full w-full">
                <MapContainer
                  center={[trackingData.driverLocation.lat, trackingData.driverLocation.lng]}
                  zoom={15}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />

                  {/* Supplier Marker */}
                  <Marker position={[trackingData.supplierLocation.lat, trackingData.supplierLocation.lng]}>
                    <Popup>
                      <div className="text-center p-3">
                        <strong className="text-xl">{order.supplier.name}</strong>
                        <br />
                        <span className="text-sm text-gray-600">🏪 Restaurant Location</span>
                      </div>
                    </Popup>
                  </Marker>

                  {/* Driver Marker */}
                  <Marker position={[trackingData.driverLocation.lat, trackingData.driverLocation.lng]}>
                    <Popup>
                      <div className="text-center p-3">
                        <strong className="text-xl">{order.driverName}</strong>
                        <br />
                        <span className="text-sm text-gray-600">🚚 Your Driver</span>
                        <br />
                        <span className="text-xs text-green-600 font-medium">Currently delivering your order!</span>
                      </div>
                    </Popup>
                  </Marker>

                  {/* Delivery Location Marker */}
                  <Marker position={[trackingData.deliveryLocation.lat, trackingData.deliveryLocation.lng]}>
                    <Popup>
                      <div className="text-center p-3">
                        <strong className="text-xl">Delivery Address</strong>
                        <br />
                        <span className="text-sm text-gray-600">📍 {order.address}</span>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              </div>

              {/* Full Screen Map Controls */}
              <div className="absolute bottom-6 right-6 flex flex-col gap-3 z-10">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleCallDriver}
                  className="p-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl text-white shadow-lg shadow-green-500/25"
                >
                  <Phone className="w-6 h-6" />
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Share Modal */}
      <AnimatePresence>
        {showShareModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowShareModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 max-w-md w-full"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-white">Share Tracking</h3>
                <button
                  onClick={() => setShowShareModal(false)}
                  className="p-2 bg-white/10 rounded-xl hover:bg-white/20 transition-colors"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
              <p className="text-white/70 mb-6">Share your order tracking link with friends and family</p>
              <div className="flex gap-3">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold"
                >
                  Copy Link
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold"
                >
                  Send SMS
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OrderTrackingPage;