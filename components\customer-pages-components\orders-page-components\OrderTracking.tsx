import { Platform, ScrollView, TouchableWithoutFeedback, StyleSheet } from 'react-native';
import { View, Text, Button, XStack, YStack, Card, H3, H4, Separator, Progress } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useMyOrdersStore } from './useMyOrdersStore';
import { useEffect, useState, useMemo } from 'react';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { Dimensions } from 'react-native';
import { Linking } from 'react-native';
import MapView, { Mark<PERSON>, Polyline } from 'react-native-maps';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';

// Responsive design hook
const useResponsive = () => {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });
    return () => subscription?.remove();
  }, []);

  const isTablet = screenData.width >= 768;
  const isDesktop = screenData.width >= 1024;

  const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
    if (isDesktop) return desktop;
    if (isTablet) return tablet;
    return mobile;
  };

  return { isTablet, isDesktop, getResponsiveValue, screenWidth: screenData.width, screenHeight: screenData.height };
};

export default function OrderTracking() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { isTablet, isDesktop, getResponsiveValue, screenHeight } = useResponsive();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === id));

  // Enhanced status configuration
  const statusConfig = useMemo(() => ({
    'Pending': {
      color: '#FFA500',
      icon: 'time-outline',
      bgColor: 'rgba(255, 165, 0, 0.1)',
      progress: 25,
      message: t('tracking.status.pendingMessage', { defaultValue: 'Order confirmed, preparing to cook' })
    },
    'Preparing': {
      color: '#FF6B6B',
      icon: 'restaurant-outline',
      bgColor: 'rgba(255, 107, 107, 0.1)',
      progress: 50,
      message: t('tracking.status.preparingMessage', { defaultValue: 'Chef is preparing your order' })
    },
    'On the Way': {
      color: '#4ECDC4',
      icon: 'car-outline',
      bgColor: 'rgba(78, 205, 196, 0.1)',
      progress: 75,
      message: t('tracking.status.onTheWayMessage', { defaultValue: 'Driver is on the way to you' })
    },
    'Delivered': {
      color: '#45B7D1',
      icon: 'checkmark-circle-outline',
      bgColor: 'rgba(69, 183, 209, 0.1)',
      progress: 100,
      message: t('tracking.status.deliveredMessage', { defaultValue: 'Order delivered successfully' })
    }
  }), [t]);

  // If order not found, redirect
  if (!order || !order.address) {
    router.replace('/orders');
    return null;
  }

  const config = statusConfig[order.status as keyof typeof statusConfig] || statusConfig['Pending'];

  // Mock customer location (you could geocode order.address here)
  const customerLocation = {
    latitude: order.address.lat,
    longitude: order.address.lng,
  };

  // Driver starting point near customer (mock)
  const [driverLocation, setDriverLocation] = useState({
    latitude: customerLocation.latitude - 0.01,
    longitude: customerLocation.longitude - 0.01,
  });

  // Simulate driver moving towards customer location
  useEffect(() => {
    const interval = setInterval(() => {
      setDriverLocation((prev) => {
        const latDiff = customerLocation.latitude - prev.latitude;
        const lonDiff = customerLocation.longitude - prev.longitude;
        const step = 0.0003; // movement per tick

        // Calculate next position
        let newLat = prev.latitude + Math.sign(latDiff) * Math.min(Math.abs(latDiff), step);
        let newLon = prev.longitude + Math.sign(lonDiff) * Math.min(Math.abs(lonDiff), step);

        // If driver close enough to customer, stop moving
        if (Math.abs(latDiff) < 0.0001 && Math.abs(lonDiff) < 0.0001) {
          clearInterval(interval);
          return prev;
        }
        return { latitude: newLat, longitude: newLon };
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // ETA countdown (mock)
  const [eta, setEta] = useState(30 * 60); // 30 minutes in seconds

  useEffect(() => {
    if (eta <= 0) return;
    const timer = setInterval(() => {
      setEta((e) => (e > 0 ? e - 1 : 0));
    }, 1000);
    return () => clearInterval(timer);
  }, [eta]);

  const formatTime = (secs: number) => {
    const m = Math.floor(secs / 60);
    const s = secs % 60;
    return `${m}m ${s}s`;
  };

  // Status steps for progress UI
  const statuses = ['Preparing', 'On the Way', 'Delivered'];

  const { height } = Dimensions.get('window');

  const [fullscreen, setFullscreen] = useState(false);

  return (
    <>
      {/* Fullscreen Map Modal */}
      {fullscreen && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999,
          }}
        >
          <MapView
            style={{ flex: 1 }}
            region={{
              latitude: customerLocation.latitude,
              longitude: customerLocation.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            <Marker
              coordinate={customerLocation}
              pinColor="green"
              title={t('tracking.deliveryLocation', { defaultValue: 'Delivery Location' })}
            />
            <Marker
              coordinate={driverLocation}
              pinColor="purple"
              title={t('tracking.driverLocation', { defaultValue: 'Driver Location' })}
            />
            <Polyline
              coordinates={[driverLocation, customerLocation]}
              strokeColor="#667eea"
              strokeWidth={4}
            />
          </MapView>

          <Button
            onPress={() => setFullscreen(false)}
            backgroundColor="rgba(0,0,0,0.7)"
            borderRadius="$10"
            style={{
              position: 'absolute',
              top: getResponsiveValue(50, 60, 70),
              right: getResponsiveValue(20, 30, 40),
              zIndex: 1000,
              padding: getResponsiveValue(12, 14, 16)
            }}
            pressStyle={{ scale: 0.95 }}
          >
            <Ionicons name="close" size={24} color="white" />
          </Button>
        </View>
      )}

      {/* Main Content */}
      {!fullscreen && (
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: getResponsiveValue(100, 120, 140)
          }}
          showsVerticalScrollIndicator={false}
        >
          {/* Enhanced Professional Header */}
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{
              paddingTop: getResponsiveValue(50, 60, 70),
              paddingBottom: getResponsiveValue(30, 40, 50),
              paddingHorizontal: getResponsiveValue(20, 30, 40),
              borderBottomLeftRadius: getResponsiveValue(25, 30, 35),
              borderBottomRightRadius: getResponsiveValue(25, 30, 35),
            }}
          >
            <MotiView
              from={{ opacity: 0, translateY: -30 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 800 }}
            >
              {/* Back Button and Title */}
              <XStack alignItems="center" justifyContent="space-between" marginBottom="$4">
                <Button
                  size="$3"
                  backgroundColor="rgba(255,255,255,0.15)"
                  borderColor="rgba(255,255,255,0.3)"
                  borderRadius="$6"
                  onPress={() => router.back()}
                  pressStyle={{ scale: 0.95 }}
                >
                  <XStack alignItems="center" gap="$2">
                    <Ionicons name="arrow-back-outline" size={20} color="white" />
                    <Text color="white" fontWeight="600">
                      {t('common.back', { defaultValue: 'Back' })}
                    </Text>
                  </XStack>
                </Button>

                <YStack alignItems="center">
                  <H3 color="white" fontWeight="800" fontSize={getResponsiveValue(20, 24, 28)}>
                    {t('tracking.trackingOrder', { defaultValue: 'Tracking Order' })}
                  </H3>
                  <Text color="rgba(255,255,255,0.8)" fontSize="$3">
                    #{order.id}
                  </Text>
                </YStack>

                <View width={getResponsiveValue(80, 90, 100)} />
              </XStack>

              {/* Live Status Card */}
              <Card
                backgroundColor="rgba(255,255,255,0.15)"
                borderColor="rgba(255,255,255,0.2)"
                borderRadius="$6"
                padding="$4"
                backdropFilter="blur(10px)"
              >
                <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
                  <XStack alignItems="center" gap="$3">
                    <MotiView
                      from={{ scale: 0.8 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'timing', duration: 1000, repeat: Infinity, repeatType: 'reverse' }}
                    >
                      <View
                        backgroundColor={config.color}
                        borderRadius="$6"
                        padding="$2"
                      >
                        <Ionicons name={config.icon as any} size={24} color="white" />
                      </View>
                    </MotiView>
                    <YStack>
                      <Text color="white" fontSize="$5" fontWeight="800">
                        {t(`orders.statuses.${order.status.toLowerCase().replace(/\s+/g, '_')}`, {
                          defaultValue: order.status
                        })}
                      </Text>
                      <Text color="rgba(255,255,255,0.8)" fontSize="$2">
                        {config.message}
                      </Text>
                    </YStack>
                  </XStack>

                  <YStack alignItems="flex-end">
                    <Text color="white" fontSize="$2" opacity={0.8}>
                      {t('tracking.eta', { defaultValue: 'ETA' })}
                    </Text>
                    <Text color="white" fontSize="$4" fontWeight="700">
                      {formatTime(eta)}
                    </Text>
                  </YStack>
                </XStack>

                {/* Progress Bar */}
                <View
                  backgroundColor="rgba(255,255,255,0.2)"
                  borderRadius="$4"
                  height={8}
                  overflow="hidden"
                  marginBottom="$2"
                >
                  <MotiView
                    from={{ width: '0%' }}
                    animate={{ width: `${config.progress}%` }}
                    transition={{ type: 'timing', duration: 1000, delay: 500 }}
                    style={{
                      height: '100%',
                      backgroundColor: config.color,
                      borderRadius: 4,
                    }}
                  />
                </View>

                <Text color="rgba(255,255,255,0.7)" fontSize="$1" textAlign="center">
                  {config.progress}% {t('tracking.complete', { defaultValue: 'Complete' })}
                </Text>
              </Card>
            </MotiView>
          </LinearGradient>

          {/* Enhanced Main Content */}
          <YStack
            padding={getResponsiveValue(16, 20, 24)}
            gap="$4"
            width={getResponsiveValue('100%', '95%', '90%')}
            alignSelf='center'
            maxWidth={isDesktop ? 1200 : undefined}
            marginTop="$4"
          >
            {/* Driver Information Card */}
            {order.driverName && (
              <MotiView
                from={{ opacity: 0, translateY: 20 }}
                animate={{ opacity: 1, translateY: 0 }}
                transition={{ type: 'timing', duration: 600, delay: 200 }}
              >
                <Card
                  backgroundColor="$backgroundStrong"
                  borderRadius="$6"
                  padding={getResponsiveValue(16, 20, 24)}
                  shadowColor="#000"
                  shadowOffset={{ width: 0, height: 6 }}
                  shadowOpacity={0.12}
                  shadowRadius={16}
                  elevation={6}
                >
                  <XStack alignItems="center" gap="$3" marginBottom="$3">
                    <View
                      backgroundColor="$color4"
                      borderRadius="$4"
                      padding="$2"
                    >
                      <Ionicons name="person-outline" size={24} color="#667eea" />
                    </View>
                    <H3 color="$gray12" fontWeight="800">
                      {t('tracking.driverInfo', { defaultValue: 'Your Driver' })}
                    </H3>
                  </XStack>

                  <XStack alignItems="center" justifyContent="space-between">
                    <XStack alignItems="center" gap="$3" flex={1}>
                      <View
                        width={50}
                        height={50}
                        backgroundColor="#667eea"
                        borderRadius="$6"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Text color="white" fontSize="$5" fontWeight="800">
                          {order.driverName.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                      <YStack flex={1}>
                        <Text color="$gray12" fontSize="$4" fontWeight="700">
                          {order.driverName}
                        </Text>
                        <Text color="$gray10" fontSize="$2">
                          {t('tracking.yourDeliveryDriver', { defaultValue: 'Your delivery driver' })}
                        </Text>
                      </YStack>
                    </XStack>

                    {order.driverPhone && (
                      <Button
                        size="$3"
                        backgroundColor="#4CAF50"
                        borderRadius="$6"
                        onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                        pressStyle={{ scale: 0.95 }}
                      >
                        <XStack alignItems="center" gap="$2">
                          <Ionicons name="call" size={18} color="white" />
                          <Text color="white" fontWeight="600" fontSize="$3">
                            {t('tracking.call', { defaultValue: 'Call' })}
                          </Text>
                        </XStack>
                      </Button>
                    )}
                  </XStack>
                </Card>
              </MotiView>
            )}

            {/* Live Map Card */}
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600, delay: 400 }}
            >
              <Card
                backgroundColor="$backgroundStrong"
                borderRadius="$6"
                padding={getResponsiveValue(12, 16, 20)}
                shadowColor="#000"
                shadowOffset={{ width: 0, height: 6 }}
                shadowOpacity={0.12}
                shadowRadius={16}
                elevation={6}
              >
                <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
                  <XStack alignItems="center" gap="$3">
                    <View
                      backgroundColor="$color4"
                      borderRadius="$4"
                      padding="$2"
                    >
                      <Ionicons name="map-outline" size={24} color="#667eea" />
                    </View>
                    <H3 color="$gray12" fontWeight="800">
                      {t('tracking.liveTracking', { defaultValue: 'Live Tracking' })}
                    </H3>
                  </XStack>

                  <Button
                    size="$2"
                    backgroundColor="rgba(102, 126, 234, 0.1)"
                    borderColor="#667eea"
                    borderWidth={1}
                    borderRadius="$4"
                    onPress={() => setFullscreen(true)}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <XStack alignItems="center" gap="$1">
                      <Ionicons name="expand-outline" size={16} color="#667eea" />
                      <Text color="#667eea" fontSize="$2" fontWeight="600">
                        {t('tracking.fullscreen', { defaultValue: 'Fullscreen' })}
                      </Text>
                    </XStack>
                  </Button>
                </XStack>

                <Text color="$gray10" fontSize="$2" marginBottom="$3" textAlign="center">
                  {t('tracking.tapMapMessage', { defaultValue: 'Tap the map to view in fullscreen mode' })}
                </Text>

                <TouchableWithoutFeedback onPress={() => setFullscreen(true)}>
                  <View
                    style={{
                      height: getResponsiveValue(250, 300, 350),
                      borderRadius: 12,
                      overflow: 'hidden',
                      borderWidth: 2,
                      borderColor: '#667eea'
                    }}
                  >
                    <MapView
                      style={{ flex: 1 }}
                      region={{
                        latitude: customerLocation.latitude,
                        longitude: customerLocation.longitude,
                        latitudeDelta: 0.01,
                        longitudeDelta: 0.01,
                      }}
                      showsUserLocation={true}
                      showsMyLocationButton={false}
                      showsCompass={false}
                      scrollEnabled={false}
                      zoomEnabled={false}
                      rotateEnabled={false}
                      pitchEnabled={false}
                    >
                      {/* Customer Location Marker */}
                      <Marker
                        coordinate={customerLocation}
                        title={t('tracking.deliveryLocation', { defaultValue: 'Delivery Location' })}
                        description={order.address?.address}
                      >
                        <View
                          style={{
                            backgroundColor: '#4CAF50',
                            borderRadius: 20,
                            padding: 8,
                            borderWidth: 3,
                            borderColor: 'white',
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.3,
                            shadowRadius: 4,
                            elevation: 5,
                          }}
                        >
                          <Ionicons name="home" size={20} color="white" />
                        </View>
                      </Marker>

                      {/* Driver Location Marker */}
                      <Marker
                        coordinate={driverLocation}
                        title={t('tracking.driverLocation', { defaultValue: 'Driver Location' })}
                        description={order.driverName || t('tracking.driver', { defaultValue: 'Driver' })}
                      >
                        <MotiView
                          from={{ scale: 0.8 }}
                          animate={{ scale: 1.2 }}
                          transition={{
                            type: 'timing',
                            duration: 1000,
                            repeat: Infinity,
                            repeatType: 'reverse'
                          }}
                        >
                          <View
                            style={{
                              backgroundColor: '#667eea',
                              borderRadius: 20,
                              padding: 8,
                              borderWidth: 3,
                              borderColor: 'white',
                              shadowColor: '#000',
                              shadowOffset: { width: 0, height: 2 },
                              shadowOpacity: 0.3,
                              shadowRadius: 4,
                              elevation: 5,
                            }}
                          >
                            <Ionicons name="car" size={20} color="white" />
                          </View>
                        </MotiView>
                      </Marker>

                      {/* Route Polyline */}
                      <Polyline
                        coordinates={[driverLocation, customerLocation]}
                        strokeColor="#667eea"
                        strokeWidth={4}
                        lineDashPattern={[5, 5]}
                      />
                    </MapView>

                    {/* Map Overlay */}
                    <View
                      style={{
                        position: 'absolute',
                        top: 10,
                        right: 10,
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        borderRadius: 20,
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                      }}
                    >
                      <Text color="white" fontSize="$2" fontWeight="600">
                        {t('tracking.liveUpdates', { defaultValue: 'Live Updates' })}
                      </Text>
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              </Card>
            </MotiView>

            {/* Enhanced Action Buttons */}
            <MotiView
              from={{ opacity: 0, translateY: 30 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600, delay: 600 }}
            >
              <YStack gap="$3" marginTop="$4">
                {/* Primary Actions */}
                <XStack gap="$3">
                  <Button
                    flex={1}
                    size="$4"
                    backgroundColor="#667eea"
                    borderRadius="$6"
                    onPress={() => router.push({
                      pathname: "/orders/order-details",
                      params: { id: order.id }
                    })}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <XStack alignItems="center" gap="$2">
                      <Ionicons name="receipt-outline" size={20} color="white" />
                      <Text color="white" fontWeight="700" fontSize="$4">
                        {t('tracking.orderDetails', { defaultValue: 'Order Details' })}
                      </Text>
                    </XStack>
                  </Button>

                  {order.driverPhone && (
                    <Button
                      flex={1}
                      size="$4"
                      backgroundColor="#4CAF50"
                      borderRadius="$6"
                      onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                      pressStyle={{ scale: 0.95 }}
                    >
                      <XStack alignItems="center" gap="$2">
                        <Ionicons name="call-outline" size={20} color="white" />
                        <Text color="white" fontWeight="700" fontSize="$4">
                          {t('tracking.callDriver', { defaultValue: 'Call Driver' })}
                        </Text>
                      </XStack>
                    </Button>
                  )}
                </XStack>

                {/* Secondary Actions */}
                <XStack gap="$3">
                  <Button
                    flex={1}
                    size="$4"
                    backgroundColor="$color4"
                    borderColor="$color6"
                    borderWidth={1}
                    borderRadius="$6"
                    onPress={() => {
                      // Implement support functionality
                      console.log('Contact support for order:', order.id);
                    }}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <XStack alignItems="center" gap="$2">
                      <Ionicons name="headset-outline" size={18} color="#667eea" />
                      <Text color="#667eea" fontWeight="600" fontSize="$3">
                        {t('tracking.support', { defaultValue: 'Support' })}
                      </Text>
                    </XStack>
                  </Button>

                  <Button
                    flex={1}
                    size="$4"
                    backgroundColor="$color3"
                    borderRadius="$6"
                    onPress={() => router.back()}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <XStack alignItems="center" gap="$2">
                      <Ionicons name="arrow-back-outline" size={18} color="$gray11" />
                      <Text color="$gray11" fontWeight="600" fontSize="$3">
                        {t('tracking.backToOrders', { defaultValue: 'Back to Orders' })}
                      </Text>
                    </XStack>
                  </Button>
                </XStack>
              </YStack>
            </MotiView>
          </YStack>
        </ScrollView>
      )}
    </>
  );
}
