import { useEffect } from 'react';
import { useCurrentUserData } from '../components/useCurrentUserData';

/**
 * Hook that automatically initializes user data and returns the current user
 * This should be used instead of useCurrentUserData directly in components that need authentication
 */
export const useAutoInitializedUser = () => {
  const store = useCurrentUserData();

  useEffect(() => {
    // Auto-initialize user data when this hook is first used
    store.autoInitialize();
  }, [store]);

  return store;
};
