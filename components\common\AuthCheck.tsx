import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useCurrentUserData } from '../useCurrentUserData';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { apiService } from '../../services/apiService';

interface AuthCheckProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export const AuthCheck: React.FC<AuthCheckProps> = ({ 
  children, 
  fallback, 
  redirectTo = '/auth/login' 
}) => {
  const { user } = useCurrentUserData();
  const { t } = useTranslation();

  if (!user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <YStack flex={1} alignItems="center" justifyContent="center" padding="$6" gap="$4">
        <Ionicons name="person-circle-outline" size={80} color="#666" />
        <Text fontSize="$6" fontWeight="600" textAlign="center">
          {t('auth.loginRequired', { defaultValue: 'Login Required' })}
        </Text>
        <Text fontSize="$4" color="$gray10" textAlign="center" lineHeight="$5">
          {t('auth.loginRequiredMessage', { 
            defaultValue: 'Please log in to access this feature.' 
          })}
        </Text>
        <Button
          size="$4"
          theme="blue"
          onPress={() => router.push(redirectTo)}
          marginTop="$4"
        >
          <Text color="white" fontWeight="600">
            {t('auth.login', { defaultValue: 'Login' })}
          </Text>
        </Button>
      </YStack>
    );
  }

  return <>{children}</>;
};

// Debug component to show authentication status
export const AuthDebugInfo: React.FC = () => {
  const { user } = useCurrentUserData();
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const accessToken = apiService.getAccessToken();

  React.useEffect(() => {
    const checkAuth = async () => {
      const auth = await apiService.isAuthenticated();
      setIsAuthenticated(auth);
    };
    checkAuth();
  }, []);

  return (
    <Card padding="$4" margin="$4" backgroundColor="$gray2">
      <Text fontSize="$5" fontWeight="600" marginBottom="$2">
        Authentication Debug Info
      </Text>
      <YStack gap="$2">
        <XStack gap="$2" alignItems="center">
          <Text fontWeight="600">User:</Text>
          <Text>{user ? `${user.firstName} ${user.lastName} (${user.email})` : 'Not logged in'}</Text>
        </XStack>
        <XStack gap="$2" alignItems="center">
          <Text fontWeight="600">API Authenticated:</Text>
          <Text color={isAuthenticated ? '$green10' : '$red10'}>
            {isAuthenticated ? 'Yes' : 'No'}
          </Text>
        </XStack>
        <XStack gap="$2" alignItems="center">
          <Text fontWeight="600">Access Token:</Text>
          <Text fontSize="$2" color="$gray10">
            {accessToken ? `${accessToken.substring(0, 20)}...` : 'None'}
          </Text>
        </XStack>
      </YStack>
    </Card>
  );
};
