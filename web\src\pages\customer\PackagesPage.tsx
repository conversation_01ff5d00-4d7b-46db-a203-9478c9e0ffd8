import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  MapPin,
  User,
  Phone,
  Clock,
  Eye,
  Search,
  X,
  Navigation,
  Truck,
  CheckCircle,
  RotateCcw,
  Calendar,
  Filter,
  ArrowLeft,
  Zap,
  Star,
  TrendingUp,
  Activity
} from 'lucide-react';

// Mock data matching mobile structure
const mockSentPackages = [
  {
    id: 'PKG_1752326131054_abc123',
    createdAt: '2025-01-12T10:30:00Z',
    pickup: {
      address: 'Askar Camp - Mosque immigrants, Nablus',
      lat: 32.2211,
      lng: 35.2544
    },
    dropoff: {
      address: 'An-Najah National University, Nablus',
      lat: 32.2278,
      lng: 35.2542
    },
    receiverName: '<PERSON>',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    status: 'Delivered' as const,
    estimatedTime: '30-45 mins',
    driverName: '<PERSON>',
    driverPhone: '0597654321',
    cost: 25
  },
  {
    id: 'PKG_1752326131055_def456',
    createdAt: '2025-01-12T14:15:00Z',
    pickup: {
      address: 'Nablus City Center',
      lat: 32.2211,
      lng: 35.2544
    },
    dropoff: {
      address: 'Balata Refugee Camp, Nablus',
      lat: 32.2100,
      lng: 35.2600
    },
    receiverName: 'Fatima Ahmad',
    receiverPhone: '+970568987654',
    packageType: 'Electronics',
    status: 'On the Way' as const,
    estimatedTime: '20-30 mins',
    driverName: 'Mohammed Ali',
    driverPhone: '0599123456',
    cost: 18
  }
];

const mockPickupRequests = [
  {
    id: 'REQ_1752326131056_ghi789',
    createdAt: '2025-01-12T16:45:00Z',
    pickup: {
      address: 'Rafidia, Nablus',
      lat: 32.2278,
      lng: 35.2542
    },
    packageType: 'Clothing',
    status: 'Preparing' as const,
    estimatedTime: '45-60 mins',
    driverName: 'Ahmad Samer',
    driverPhone: '0595956014',
    cost: 15,
    notes: 'Please call when you arrive'
  }
];

type PackageStatus = 'All' | 'Delivered' | 'On the Way' | 'Preparing' | 'Pending';
type TabType = 'sent' | 'pickup';

const PackagesPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>('sent');
  const [selectedFilter, setSelectedFilter] = useState<PackageStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Handle scroll for header animation with ultra-smooth debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  const currentData = activeTab === 'sent' ? mockSentPackages : mockPickupRequests;

  const filteredPackages = useMemo(() => {
    let packages = selectedFilter === 'All' ? currentData : currentData.filter(pkg => pkg.status === selectedFilter);

    if (searchQuery.trim()) {
      packages = packages.filter(pkg =>
        pkg.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.pickup.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ('dropoff' in pkg && pkg.dropoff.address.toLowerCase().includes(searchQuery.toLowerCase())) ||
        ('receiverName' in pkg && pkg.receiverName.toLowerCase().includes(searchQuery.toLowerCase())) ||
        pkg.packageType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return packages;
  }, [activeTab, selectedFilter, searchQuery, currentData]);

  const filters: { key: PackageStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Packages', color: 'bg-gray-100 text-gray-700' },
    { key: 'Delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'On the Way', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'Preparing', label: 'Preparing', color: 'bg-blue-100 text-blue-700' },
    { key: 'Pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={20} className="text-green-300" />;
      case 'On the Way': return <Truck size={20} className="text-orange-300" />;
      case 'Preparing': return <Clock size={20} className="text-blue-300" />;
      default: return <Package size={20} className="text-yellow-300" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-300 bg-green-500/20 border-green-400/50';
      case 'On the Way': return 'text-orange-300 bg-orange-500/20 border-orange-400/50';
      case 'Preparing': return 'text-blue-300 bg-blue-500/20 border-blue-400/50';
      default: return 'text-yellow-300 bg-yellow-500/20 border-yellow-400/50';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-white/10 rounded-full"
                animate={{
                  y: [0, -100, 0],
                  x: [0, Math.random() * 100 - 50, 0],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  delay: Math.random() * 10,
                }}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
              />
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          {/* Hero Header */}
          <div className="pt-20 pb-16 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <div className="inline-flex items-center gap-3 mb-6">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-2xl"
                  >
                    <Package className="text-white" size={32} />
                  </motion.div>
                  <div>
                    <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
                      My Packages
                    </h1>
                    <motion.div
                      animate={{ width: ["0%", "100%"] }}
                      transition={{ duration: 2, delay: 0.5 }}
                      className="h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full mt-2"
                    />
                  </div>
                </div>
                <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                  Track your deliveries with real-time updates and comprehensive package management
                </p>
              </motion.div>

              {/* Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                      <CheckCircle className="text-green-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {mockSentPackages.filter(p => p.status === 'Delivered').length}
                      </p>
                      <p className="text-gray-300 text-sm">Delivered</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                      <Truck className="text-orange-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {mockSentPackages.filter(p => p.status === 'On the Way').length}
                      </p>
                      <p className="text-gray-300 text-sm">In Transit</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                      <Activity className="text-blue-400" size={24} />
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-white">
                        {mockSentPackages.length + mockPickupRequests.length}
                      </p>
                      <p className="text-gray-300 text-sm">Total Packages</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Control Panel */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            {/* Tabs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mb-8"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="flex justify-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setActiveTab('sent')}
                    className={`px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 ${
                      activeTab === 'sent'
                        ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Package size={24} />
                      Sent Packages
                    </div>
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setActiveTab('pickup')}
                    className={`px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 ${
                      activeTab === 'pickup'
                        ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <RotateCcw size={24} />
                      Pickup Requests
                    </div>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Search Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="mb-8"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
                <div className="flex items-center gap-4 p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <Search size={24} className="text-white" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search packages, addresses, or recipients..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-white placeholder-gray-300 outline-none text-lg"
                  />
                  {searchQuery.trim() && (
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setSearchQuery('')}
                      className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <X size={20} className="text-white" />
                    </motion.button>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Filter Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mb-12"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                    <Filter size={20} className="text-white" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">Filter by Status</h3>
                </div>
                <div className="flex flex-wrap gap-3">
                  {filters.map((filter) => (
                    <motion.button
                      key={filter.key}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedFilter(filter.key)}
                      className={`px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 ${
                        selectedFilter === filter.key
                          ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-2xl'
                          : 'bg-white/20 text-white hover:bg-white/30'
                      }`}
                    >
                      {filter.label}
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "64px",
            zIndex: isHeaderCompact ? 50 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-between"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/customer')}
                className="flex items-center gap-3 text-white hover:text-gray-300 transition-colors"
              >
                <ArrowLeft size={24} />
                <span className="font-medium">Back to Dashboard</span>
              </motion.button>

              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  scale: isHeaderCompact ? 1 : 0.8,
                }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-4"
              >
                <div className="flex items-center gap-3">
                  <Package className="text-white" size={24} />
                  <span className="text-white font-semibold text-lg">My Packages</span>
                </div>

                {/* Quick search in header */}
                <div className="hidden md:flex items-center gap-2 bg-white/10 rounded-lg px-4 py-2">
                  <Search size={16} className="text-gray-300" />
                  <input
                    type="text"
                    placeholder="Quick search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-transparent text-white placeholder-gray-300 outline-none text-sm w-32"
                  />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Packages List */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="space-y-6"
          >
            {filteredPackages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white/10 backdrop-blur-lg rounded-3xl p-16 text-center border border-white/20"
              >
                <motion.div
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-24 h-24 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                  <Package size={48} className="text-white" />
                </motion.div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  {activeTab === 'sent' ? 'No Sent Packages' : 'No Pickup Requests'}
                </h3>
                <p className="text-gray-300 text-lg">
                  {searchQuery.trim()
                    ? `No packages match "${searchQuery}"`
                    : activeTab === 'sent'
                      ? "You haven't sent any packages yet"
                      : "You haven't requested any pickups yet"
                  }
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/customer/send-package')}
                  className="mt-8 px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl shadow-2xl"
                >
                  Send Your First Package
                </motion.button>
              </motion.div>
            ) : (
              <AnimatePresence>
                {filteredPackages.map((pkg, index) => (
                  <motion.div
                    key={pkg.id}
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -30, scale: 0.9 }}
                    transition={{
                      duration: 0.6,
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 100
                    }}
                    whileHover={{
                      y: -8,
                      scale: 1.02,
                      transition: { duration: 0.3 }
                    }}
                    className="bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 overflow-hidden hover:bg-white/15 transition-all duration-500 group"
                  >
                    {/* Package Header */}
                    <div className="p-8 border-b border-white/10">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <motion.div
                            whileHover={{ rotate: 360 }}
                            transition={{ duration: 0.6 }}
                            className="w-16 h-16 rounded-2xl flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 shadow-2xl"
                          >
                            {activeTab === 'sent' ? (
                              <Package className="text-white" size={28} />
                            ) : (
                              <RotateCcw className="text-white" size={28} />
                            )}
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-2xl text-white mb-1">#{pkg.id.slice(-8)}</h3>
                            <div className="flex items-center gap-2">
                              <span className="text-gray-300 text-lg">{pkg.packageType}</span>
                              <Star className="text-yellow-400" size={16} />
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            className={`inline-flex items-center gap-3 px-4 py-2 rounded-xl text-lg font-semibold border-2 ${getStatusColor(pkg.status)} backdrop-blur-sm`}
                          >
                            {getStatusIcon(pkg.status)}
                            {pkg.status}
                          </motion.div>
                          <p className="text-gray-300 text-sm mt-2">
                            {formatDate(pkg.createdAt).date} • {formatDate(pkg.createdAt).time}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Compact Package Details - Most Important Info Only */}
                    <div className="p-6">
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* Essential Route Info */}
                        <div className="space-y-4">
                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="flex items-center gap-3 mb-3">
                              <MapPin size={16} className="text-blue-400" />
                              <span className="text-gray-300 text-sm font-medium">From</span>
                            </div>
                            <p className="text-white text-sm leading-relaxed truncate">{pkg.pickup.address}</p>
                          </div>

                          {activeTab === 'sent' && 'dropoff' in pkg && (
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                              <div className="flex items-center gap-3 mb-3">
                                <Navigation size={16} className="text-green-400" />
                                <span className="text-gray-300 text-sm font-medium">To</span>
                              </div>
                              <p className="text-white text-sm leading-relaxed truncate">{pkg.dropoff.address}</p>
                            </div>
                          )}
                        </div>

                        {/* Essential Package Info */}
                        <div className="space-y-4">
                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-gray-300 text-xs">Type</p>
                                <p className="text-white font-semibold text-sm">{pkg.packageType}</p>
                              </div>
                              <div>
                                <p className="text-gray-300 text-xs">Est. Time</p>
                                <p className="text-white font-semibold text-sm">{pkg.estimatedTime}</p>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-gray-300 text-xs">Total Cost</p>
                                <p className="text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                                  ₪{pkg.cost}
                                </p>
                              </div>
                              {activeTab === 'sent' && 'receiverName' in pkg && (
                                <div className="text-right">
                                  <p className="text-gray-300 text-xs">Recipient</p>
                                  <p className="text-white font-semibold text-sm">{pkg.receiverName}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>



                      {/* Action Buttons */}
                      <div className="mt-8 pt-6 border-t border-white/20">
                        <div className="flex flex-col sm:flex-row gap-4">
                          <motion.button
                            whileHover={{ scale: 1.025 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => {
                              window.location.href = `/customer/package-tracking?packageId=${pkg.id}`;
                            }}
                            className="flex-1 px-8 py-4 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-bold rounded-xl shadow-2xl transition-all duration-300 flex items-center justify-center gap-3 text-lg"
                          >
                            <Eye size={24} />
                            Track Package
                          </motion.button>

                          <motion.button
                            whileHover={{ scale: 1.025 }}
                            whileTap={{ scale: 0.975 }}
                            onClick={() => navigate(`/customer/packages/details/${pkg.id}?type=${activeTab}`)}
                            className="px-6 py-4 bg-white/20 hover:bg-white/30 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2"
                          >
                            <TrendingUp size={20} />
                            Details
                          </motion.button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            )}
          </motion.div>
        </div>
      </div>

      {/* Floating Action Button */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: "spring", stiffness: 200 }}
        className="fixed bottom-8 right-8 z-40"
      >
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate('/customer/send-package')}
          className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full shadow-2xl flex items-center justify-center text-white"
        >
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Zap size={28} />
          </motion.div>
        </motion.button>
      </motion.div>
    </>
  );
};

export default PackagesPage;