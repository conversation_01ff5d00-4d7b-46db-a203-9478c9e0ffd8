import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Store,
  TrendingUp,
  Package,
  Star,
  Clock,
  DollarSign,
  Eye,
  Plus,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle2,
  Timer,
  Truck,
  Play,
  Pause,
  MapPin,
  Phone,
  ShoppingBag,
  Zap,
  Award,
  Target,
  Sparkles,
  Crown,
  Flame,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  TrendingDown,

} from 'lucide-react';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';
import { useOrdersStore } from '../../stores/ordersStore';
import { useNavigate } from 'react-router-dom';

// Import suppliers data from local data directory
import { suppliersData } from '../../data/suppliersData';

// Modern Glass Card Component - Proper z-index for header compatibility
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10, // Lower z-index to not interfere with header
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Enhanced Metric Card Component
const MetricCard: React.FC<{
  icon: React.ComponentType<any>;
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  gradient: string;
}> = ({ icon: Icon, title, value, change, changeType = 'neutral', gradient }) => (
  <GlassCard gradient={gradient} className="p-8 group">
    <div className="flex items-center justify-between mb-6">
      <motion.div
        className="p-4 bg-white/25 rounded-2xl border border-white/20 group-hover:bg-white/35 transition-all duration-300"
        whileHover={{ scale: 1.1, rotate: 5 }}
      >
        <Icon size={28} className="text-white drop-shadow-lg" />
      </motion.div>
      {change && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`flex items-center gap-2 px-3 py-2 rounded-xl border text-sm font-bold ${
            changeType === 'positive'
              ? 'text-green-200 bg-green-500/20 border-green-400/30' :
            changeType === 'negative'
              ? 'text-red-200 bg-red-500/20 border-red-400/30'
              : 'text-blue-200 bg-blue-500/20 border-blue-400/30'
          }`}
        >
          {changeType === 'positive' && <ArrowUp size={16} />}
          {changeType === 'negative' && <ArrowDown size={16} />}
          {change}
        </motion.div>
      )}
    </div>
    <div className="text-white">
      <motion.p
        className="text-4xl font-black mb-2 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent"
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {value}
      </motion.p>
      <p className="text-white/90 text-base font-semibold">{title}</p>
    </div>
  </GlassCard>
);

// Enhanced Quick Action Button Component
const QuickActionButton: React.FC<{
  icon: React.ComponentType<any>;
  label: string;
  onClick: () => void;
  gradient: string;
}> = ({ icon: Icon, label, onClick, gradient }) => (
  <motion.button
    onClick={onClick}
    whileHover={{
      scale: 1.08,
      y: -8,
      rotateY: 5,
      boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
    }}
    whileTap={{ scale: 0.95 }}
    transition={{ type: "spring", stiffness: 300, damping: 20 }}
    className={`relative group p-8 bg-gradient-to-br ${gradient} rounded-3xl shadow-2xl border border-white/30 overflow-hidden transform-gpu`}
  >
    {/* Animated background overlay */}
    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-500 transform -translate-x-full group-hover:translate-x-full" />

    {/* Glow effect */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

    <div className="relative z-10 text-center">
      <motion.div
        className="mb-4 flex justify-center"
        whileHover={{ scale: 1.2, rotate: 10 }}
        transition={{ type: "spring", stiffness: 400, damping: 15 }}
      >
        <div className="p-3 bg-white/20 rounded-2xl border border-white/30">
          <Icon size={36} className="text-white drop-shadow-lg" />
        </div>
      </motion.div>
      <p className="text-white font-bold text-base tracking-wide">{label}</p>
    </div>
  </motion.button>
);

// Order Status Section Component
const OrderStatusSection: React.FC<{
  title: string;
  icon: React.ComponentType<any>;
  orders: any[];
  gradient: string;
  renderOrderCard: (order: any) => React.ReactNode;
}> = ({ title, icon: Icon, orders, gradient, renderOrderCard }) => (
  <GlassCard gradient={gradient} className="p-8">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-white/20 rounded-2xl">
          <Icon size={28} className="text-white" />
        </div>
        <div>
          <h3 className="text-white text-2xl font-black">{title}</h3>
          <p className="text-white/80 text-sm font-medium">{orders.length} orders</p>
        </div>
      </div>
      {orders.length > 0 && (
        <div className="bg-white/20 rounded-2xl px-4 py-2">
          <span className="text-white text-lg font-black">{orders.length}</span>
        </div>
      )}
    </div>

    <div className="space-y-4">
      <AnimatePresence>
        {orders.length > 0 ? (
          orders.slice(0, 2).map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {renderOrderCard(order)}
            </motion.div>
          ))
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <div className="bg-white/10 rounded-2xl p-4 w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <CheckCircle2 size={24} className="text-green-400" />
            </div>
            <p className="text-white/80 font-medium">All clear!</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  </GlassCard>
);

const SupplierHomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { orders } = useOrdersStore();

  const [storeOpen, setStoreOpen] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Find supplier data based on user's supplierId
  const supplierData = suppliersData.find((supplier) => supplier.id === user?.supplierId);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Filter orders by status (for now using all orders as mock data)
  const newOrders = orders.filter((o) => o.status === 'Pending');
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing');
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way');
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered');
  const allSupplierOrders = orders; // In real app, filter by supplierId

  // Calculate today's stats
  const today = new Date();
  const todayOrders = allSupplierOrders.filter(order => {
    const orderDate = new Date(order.createdAt || today);
    return orderDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const avgRating = 4.8; // This would come from reviews data
  const totalProducts = 45; // This would come from products data

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Preparing': return "bg-yellow-500";
      case 'On the Way': return "bg-orange-500";
      case 'Delivered': return "bg-green-500";
      default: return "bg-red-500";
    }
  };

  const renderOrderCard = (order: any) => (
    <motion.div
      key={order.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        scale: 1.03,
        y: -6,
        boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="group relative bg-white/15 border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu"
    >
      {/* Enhanced Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/25 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1200" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative z-10">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <motion.div
              className="p-3 bg-white/25 rounded-2xl border border-white/40"
              whileHover={{ scale: 1.1, rotate: 5 }}
            >
              <Package size={20} className="text-white" />
            </motion.div>
            <span className="text-white font-black text-xl">#{order.id}</span>
          </div>
          <motion.span
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            whileHover={{ scale: 1.1 }}
            className={`px-4 py-2 rounded-2xl text-sm font-bold text-white shadow-lg border border-white/20 ${getStatusColor(order.status)}`}
          >
            {order.status}
          </motion.span>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <motion.div
            className="flex items-center gap-3 text-white/95 p-3 bg-white/10 rounded-2xl border border-white/20"
            whileHover={{ scale: 1.02 }}
          >
            <div className="p-2 bg-white/25 rounded-xl">
              <Package size={14} className="text-white" />
            </div>
            <span className="text-sm font-semibold">{order.items.length} items</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-3 text-white/95 p-3 bg-white/10 rounded-2xl border border-white/20"
            whileHover={{ scale: 1.02 }}
          >
            <div className="p-2 bg-white/25 rounded-xl">
              <DollarSign size={14} className="text-white" />
            </div>
            <span className="text-sm font-semibold">₪{order.total.toFixed(2)}</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-3 text-white/95 p-3 bg-white/10 rounded-2xl border border-white/20"
            whileHover={{ scale: 1.02 }}
          >
            <div className="p-2 bg-white/25 rounded-xl">
              <MapPin size={14} className="text-white" />
            </div>
            <span className="text-sm font-semibold truncate">{order.address || 'No address'}</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-3 text-white/95 p-3 bg-white/10 rounded-2xl border border-white/20"
            whileHover={{ scale: 1.02 }}
          >
            <div className="p-2 bg-white/25 rounded-xl">
              <Phone size={14} className="text-white" />
            </div>
            <span className="text-sm font-semibold">{order.phone}</span>
          </motion.div>
        </div>

        <motion.button
          onClick={() => navigate(`/supplier/order-details/${order.id}`)}
          whileHover={{
            scale: 1.05,
            y: -2,
            boxShadow: "0 10px 25px -5px rgba(139, 92, 246, 0.4)"
          }}
          whileTap={{ scale: 0.98 }}
          transition={{ type: "spring", stiffness: 400, damping: 20 }}
          className="w-full px-6 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/40 hover:from-purple-700 hover:to-blue-700 transition-all duration-300 flex items-center justify-center gap-3 font-bold shadow-xl"
        >
          <Eye size={18} />
          View Details
          <ChevronRight size={16} />
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.05) rotate(1deg); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }


      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container - Normal z-index to not interfere with header */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="w-full space-y-10">

            {/* Enhanced Modern Header Section */}
            <GlassCard gradient="from-white/25 to-white/15" className="p-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  {/* Enhanced Store Icon with Status */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <motion.div
                      className="relative p-6 bg-white/25 border border-white/40 rounded-3xl"
                      whileHover={{ scale: 1.05, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      <Store size={48} className="text-white drop-shadow-lg" />

                      {/* Enhanced Status Indicator */}
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4 }}
                        className={`absolute -top-3 -right-3 w-8 h-8 ${storeOpen ? 'bg-green-500' : 'bg-red-500'} rounded-full border-3 border-white flex items-center justify-center shadow-lg`}
                      >
                        <motion.div
                          animate={{ scale: [1, 1.3, 1], rotate: [0, 360, 0] }}
                          transition={{ duration: 3, repeat: Infinity }}
                        >
                          {storeOpen ? <CheckCircle2 size={16} className="text-white" /> : <AlertCircle size={16} className="text-white" />}
                        </motion.div>
                      </motion.div>
                    </motion.div>

                    {/* Enhanced Glow Effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                      animate={{ opacity: [0.3, 0.6, 0.3] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </motion.div>

                  {/* Enhanced Welcome Text */}
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-5xl font-black mb-3 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                    >
                      Welcome back, {supplierData?.name || 'Supplier'}!
                      <motion.span
                        animate={{ rotate: [0, 20, -20, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                        className="inline-block ml-2"
                      >
                        👋
                      </motion.span>
                    </motion.h1>
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-center gap-6"
                    >
                      <motion.div
                        className={`flex items-center gap-3 px-4 py-2.5 ${storeOpen ? 'bg-green-500/25' : 'bg-red-500/25'} rounded-2xl border border-white/30 shadow-lg`}
                        whileHover={{ scale: 1.05 }}
                      >
                        <motion.div
                          className={`w-3 h-3 ${storeOpen ? 'bg-green-400' : 'bg-red-400'} rounded-full shadow-lg`}
                          animate={{ scale: [1, 1.3, 1] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                        />
                        <span className="text-white text-base font-bold tracking-wide">
                          {storeOpen ? 'OPEN' : 'CLOSED'}
                        </span>
                      </motion.div>
                      <motion.div
                        className="flex items-center gap-3 bg-white/15 px-4 py-2.5 rounded-2xl border border-white/30 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Clock size={18} className="text-white/90" />
                        <span className="text-white text-base font-semibold">
                          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </motion.div>
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Store Toggle Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: 10,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)"
                  }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setStoreOpen(!storeOpen)}
                  className="group relative p-6 bg-white/25 border border-white/40 rounded-3xl hover:bg-white/35 transition-all duration-300 shadow-xl"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <motion.div
                    className="relative z-10"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    {storeOpen ? (
                      <Pause size={28} className="text-white drop-shadow-lg" />
                    ) : (
                      <Play size={28} className="text-white drop-shadow-lg" />
                    )}
                  </motion.div>

                  {/* Glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-purple-400/30 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </motion.button>
              </div>
            </GlassCard>

            {/* Enhanced Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-10">
              <MetricCard
                icon={TrendingUp}
                title="Today's Revenue"
                value={`₪${todayRevenue.toFixed(0)}`}
                change="+12%"
                changeType="positive"
                gradient="from-emerald-500/25 to-green-500/25"
              />
              <MetricCard
                icon={Package}
                title="Today's Orders"
                value={todayOrders.length}
                change={`${newOrders.length} pending`}
                changeType="neutral"
                gradient="from-blue-500/25 to-indigo-500/25"
              />
              <MetricCard
                icon={Star}
                title="Rating"
                value={avgRating}
                change="Excellent"
                changeType="positive"
                gradient="from-yellow-500/25 to-orange-500/25"
              />
            </div>

            {/* Enhanced Quick Actions Section */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-10 mt-10">
              <div className="flex items-center gap-4 mb-8">
                <motion.div
                  className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <Zap size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-3xl font-black bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                  Quick Actions
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-white/10 rounded-full"
                >
                  <Sparkles size={20} className="text-yellow-300" />
                </motion.div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                <QuickActionButton
                  icon={Plus}
                  label="Add Product"
                  onClick={() => navigate('/supplier/products/add')}
                  gradient="from-emerald-500 to-green-600"
                />
                <QuickActionButton
                  icon={BarChart3}
                  label="Analytics"
                  onClick={() => navigate('/supplier/analytics')}
                  gradient="from-blue-500 to-indigo-600"
                />
                <QuickActionButton
                  icon={ShoppingBag}
                  label="Products"
                  onClick={() => navigate('/supplier/products')}
                  gradient="from-orange-500 to-red-600"
                />
                <QuickActionButton
                  icon={Settings}
                  label="Settings"
                  onClick={() => navigate('/supplier/profile')}
                  gradient="from-purple-500 to-pink-600"
                />
              </div>
            </GlassCard>

            {/* New Orders Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <OrderStatusSection
                title="🚨 New Orders"
                icon={AlertCircle}
                orders={newOrders}
                gradient="from-red-500/20 to-pink-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            {/* Enhanced Performance Highlights */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-10 mt-10">
              <div className="flex items-center gap-4 mb-8">
                <motion.div
                  className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -10 }}
                >
                  <BarChart3 size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-3xl font-black bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  Today's Performance
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="p-2 bg-white/10 rounded-full"
                >
                  <Award size={20} className="text-blue-300" />
                </motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <motion.div
                  className="text-center p-8 bg-white/15 rounded-3xl border border-white/30 group"
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <motion.div
                      className="p-3 bg-orange-500/20 rounded-2xl border border-orange-400/30"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Target size={24} className="text-orange-400" />
                    </motion.div>
                    <p className="text-white/90 text-base font-bold tracking-wide">AVG ORDER</p>
                  </div>
                  <motion.p
                    className="text-white text-3xl font-black bg-gradient-to-r from-white to-orange-200 bg-clip-text text-transparent"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    ₪{todayOrders.length > 0 ? (todayRevenue / todayOrders.length).toFixed(0) : '0'}
                  </motion.p>
                </motion.div>

                <motion.div
                  className="text-center p-8 bg-white/15 rounded-3xl border border-white/30 group"
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <motion.div
                      className="p-3 bg-green-500/20 rounded-2xl border border-green-400/30"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <CheckCircle2 size={24} className="text-green-400" />
                    </motion.div>
                    <p className="text-white/90 text-base font-bold tracking-wide">COMPLETION</p>
                  </div>
                  <motion.p
                    className="text-white text-3xl font-black bg-gradient-to-r from-white to-green-200 bg-clip-text text-transparent"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    {allSupplierOrders.length > 0
                      ? Math.round((deliveredOrders.length / allSupplierOrders.length) * 100)
                      : 0}%
                  </motion.p>
                </motion.div>

                <motion.div
                  className="text-center p-8 bg-white/15 rounded-3xl border border-white/30 group"
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <motion.div
                      className="p-3 bg-purple-500/20 rounded-2xl border border-purple-400/30"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Package size={24} className="text-purple-400" />
                    </motion.div>
                    <p className="text-white/90 text-base font-bold tracking-wide">PRODUCTS</p>
                  </div>
                  <motion.p
                    className="text-white text-3xl font-black bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    {totalProducts}
                  </motion.p>
                </motion.div>
              </div>
            </GlassCard>

            {/* Other Order Sections */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <OrderStatusSection
                title="In Preparing"
                icon={Timer}
                orders={inPreparingOrders}
                gradient="from-yellow-500/20 to-orange-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <OrderStatusSection
                title="On The Way"
                icon={Truck}
                orders={onTheWayOrders}
                gradient="from-orange-500/20 to-red-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <OrderStatusSection
                title="Delivered"
                icon={CheckCircle2}
                orders={deliveredOrders}
                gradient="from-green-500/20 to-emerald-500/20"
                renderOrderCard={renderOrderCard}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupplierHomePage;
