import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bell,
  BellRing,
  Package,
  CreditCard,
  Settings,
  Star,
  Megaphone,
  Trash2,
  Check,
  CheckCheck,
  Clock,
  AlertCircle,
  X,
  Filter,
  Zap,
  Sparkles,
  Crown,
  Target
} from 'lucide-react';
import { useNotificationsStore, type Notification } from '../../../stores/notificationsStore';

// Enhanced Glass Card Component with Extreme Effects
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
  onClick?: () => void;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true, onClick }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    onClick={onClick}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl ${className.includes('overflow-visible') ? 'overflow-visible' : 'overflow-hidden'} ${className} ${onClick ? 'cursor-pointer' : ''}`}
    style={{
      zIndex: 10,
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Custom Badge Component
const Badge: React.FC<{
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error';
  className?: string;
}> = ({ children, variant = 'default', className = '' }) => {
  const variants = {
    default: 'bg-gradient-to-r from-blue-500/25 to-indigo-500/25 text-blue-200 border-blue-400/40 shadow-lg shadow-blue-500/20',
    success: 'bg-gradient-to-r from-emerald-500/25 to-green-500/25 text-emerald-200 border-emerald-400/40 shadow-lg shadow-emerald-500/20',
    warning: 'bg-gradient-to-r from-amber-500/25 to-yellow-500/25 text-amber-200 border-amber-400/40 shadow-lg shadow-amber-500/20',
    error: 'bg-gradient-to-r from-rose-500/25 to-red-500/25 text-rose-200 border-rose-400/40 shadow-lg shadow-rose-500/20',
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold border ${variants[variant]} ${className}`}>
      {children}
    </span>
  );
};

const NotificationsPage: React.FC = () => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    getNotificationsByType
  } = useNotificationsStore();

  // Clear localStorage if there are any data issues (development helper)
  React.useEffect(() => {
    // Check if notifications have valid timestamps
    const hasInvalidTimestamps = notifications.some(n => {
      const date = n.timestamp instanceof Date ? n.timestamp : new Date(n.timestamp);
      return isNaN(date.getTime());
    });

    if (hasInvalidTimestamps) {
      console.warn('Found invalid timestamps, clearing notifications storage...');
      localStorage.removeItem('wasel-notifications-storage');
      window.location.reload();
    }
  }, [notifications]);

  const [selectedFilter, setSelectedFilter] = useState<'all' | Notification['type']>('all');

  const filteredNotifications = selectedFilter === 'all' 
    ? notifications 
    : getNotificationsByType(selectedFilter);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'order': return Package;
      case 'payment': return CreditCard;
      case 'system': return Settings;
      case 'review': return Star;
      case 'promotion': return Megaphone;
      default: return Bell;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'from-emerald-400 via-green-500 to-teal-600';
      case 'payment': return 'from-blue-400 via-indigo-500 to-purple-600';
      case 'system': return 'from-orange-400 via-amber-500 to-yellow-600';
      case 'review': return 'from-violet-400 via-purple-500 to-fuchsia-600';
      case 'promotion': return 'from-rose-400 via-pink-500 to-red-600';
      default: return 'from-slate-400 via-gray-500 to-zinc-600';
    }
  };

  const getNotificationBorderColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'border-emerald-400/50';
      case 'payment': return 'border-blue-400/50';
      case 'system': return 'border-orange-400/50';
      case 'review': return 'border-violet-400/50';
      case 'promotion': return 'border-rose-400/50';
      default: return 'border-gray-400/50';
    }
  };

  const getNotificationAccentColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'text-emerald-300';
      case 'payment': return 'text-blue-300';
      case 'system': return 'text-orange-300';
      case 'review': return 'text-violet-300';
      case 'promotion': return 'text-rose-300';
      default: return 'text-gray-300';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-400 to-rose-500 shadow-lg shadow-red-500/50';
      case 'medium': return 'bg-gradient-to-r from-amber-400 to-orange-500 shadow-lg shadow-amber-500/50';
      case 'low': return 'bg-gradient-to-r from-emerald-400 to-green-500 shadow-lg shadow-emerald-500/50';
      default: return 'bg-gradient-to-r from-slate-400 to-gray-500 shadow-lg shadow-gray-500/50';
    }
  };

  const formatTimeAgo = (timestamp: Date | string) => {
    const now = new Date();
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

    // Safety check to ensure we have a valid date
    if (isNaN(date.getTime())) {
      return 'Unknown time';
    }

    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleNotificationPress = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    // Handle navigation based on notification type
    if (notification.orderId) {
      // Navigate to order details
      console.log('Navigate to order:', notification.orderId);
    }
  };

  const handleDeleteNotification = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this notification?')) {
      deleteNotification(id);
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all notifications?')) {
      clearAll();
    }
  };

  const filterOptions = [
    { value: 'all', label: 'All Notifications', count: notifications.length },
    { value: 'order', label: 'Orders', count: getNotificationsByType('order').length },
    { value: 'payment', label: 'Payments', count: getNotificationsByType('payment').length },
    { value: 'review', label: 'Reviews', count: getNotificationsByType('review').length },
    { value: 'system', label: 'System', count: getNotificationsByType('system').length },
    { value: 'promotion', label: 'Promotions', count: getNotificationsByType('promotion').length },
  ];

  return (
    <>
      {/* EXTREME Premium CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(3deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 80px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse-ring {
          0% { transform: scale(1); opacity: 1; }
          100% { transform: scale(2); opacity: 0; }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.05) rotate(2deg); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
        @keyframes notification-bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }
        @keyframes priority-pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.3); opacity: 0.7; }
        }
        @keyframes slide-in-right {
          0% { transform: translateX(100%); opacity: 0; }
          100% { transform: translateX(0); opacity: 1; }
        }
        @keyframes slide-out-left {
          0% { transform: translateX(0); opacity: 1; }
          100% { transform: translateX(-100%); opacity: 0; }
        }
        @keyframes aurora {
          0%, 100% {
            background-position: 0% 50%;
            filter: hue-rotate(0deg);
          }
          25% {
            background-position: 100% 50%;
            filter: hue-rotate(90deg);
          }
          50% {
            background-position: 100% 100%;
            filter: hue-rotate(180deg);
          }
          75% {
            background-position: 0% 100%;
            filter: hue-rotate(270deg);
          }
        }
        .aurora-bg {
          background: linear-gradient(45deg,
            rgba(139, 92, 246, 0.3),
            rgba(59, 130, 246, 0.3),
            rgba(16, 185, 129, 0.3),
            rgba(245, 158, 11, 0.3),
            rgba(239, 68, 68, 0.3),
            rgba(139, 92, 246, 0.3)
          );
          background-size: 400% 400%;
          animation: aurora 15s ease infinite;
        }
        .notification-glow {
          box-shadow:
            0 0 20px rgba(139, 92, 246, 0.3),
            0 0 40px rgba(139, 92, 246, 0.2),
            0 0 60px rgba(139, 92, 246, 0.1);
        }
        .priority-high {
          animation: priority-pulse 2s ease-in-out infinite;
        }
        .notification-enter {
          animation: slide-in-right 0.5s ease-out;
        }
        .notification-exit {
          animation: slide-out-left 0.3s ease-in;
        }
      `}</style>

      {/* EXTREME Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-indigo-900 to-purple-900 aurora-bg">
        {/* Animated gradient orbs with enhanced colors */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-amber-400/40 via-orange-500/40 to-red-500/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/4 right-0 w-80 h-80 bg-gradient-to-br from-violet-500/50 via-purple-600/50 to-fuchsia-600/50 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-cyan-500/40 via-blue-600/40 to-indigo-600/40 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 0.9, 1.1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 6
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-emerald-500/30 via-teal-600/30 to-green-600/30 rounded-full blur-3xl"
        />
      </div>

      <div className="relative z-10 min-h-screen">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <div className="space-y-8">
            {/* Ultra Enhanced Notifications Header */}
            <motion.div
              initial={{ opacity: 0, y: -40, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            >
              <GlassCard
                gradient="from-indigo-500/25 via-purple-500/20 to-pink-500/25"
                className="p-8 border-indigo-400/40 shadow-2xl shadow-purple-500/20"
                hoverEffect={false}
              >
                {/* Enhanced Decorative Background Elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-amber-400/15 via-orange-500/10 to-transparent rounded-full -translate-y-20 translate-x-20" />
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-br from-violet-400/10 via-purple-500/8 to-transparent rounded-full translate-y-16 -translate-x-16" />
                <div className="absolute top-1/2 right-1/4 w-24 h-24 bg-gradient-to-br from-cyan-400/8 to-transparent rounded-full" />

                <div className="space-y-6">
                  {/* Main Header Content */}
                  <div className="flex items-center gap-6">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                      className="relative"
                    >
                      <div className="bg-gradient-to-br from-amber-400/30 via-orange-500/20 to-red-500/30 border-2 border-amber-300/40 rounded-2xl p-4 shadow-lg shadow-orange-500/20">
                        <Bell size={36} className="text-amber-100" />
                      </div>

                      {unreadCount > 0 && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.6, type: 'spring', damping: 10 }}
                          className="absolute -top-2 -right-2"
                        >
                          <div className="bg-gradient-to-r from-red-500 to-red-600 border-2 border-white rounded-full px-2 py-1">
                            <span className="text-white text-xs font-bold">
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </span>
                          </div>
                        </motion.div>
                      )}
                    </motion.div>

                    <div className="flex-1 space-y-2">
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6, duration: 0.6 }}
                        className="text-white text-4xl font-black"
                      >
                        Notifications
                      </motion.h1>

                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7, duration: 0.6 }}
                        className="flex items-center gap-3"
                      >
                        {unreadCount > 0 ? (
                          <>
                            <Badge variant="error">
                              {unreadCount} NEW
                            </Badge>
                            <span className="text-white/90 font-medium">
                              You have unread messages
                            </span>
                          </>
                        ) : (
                          <>
                            <Badge variant="success">
                              ✓ ALL READ
                            </Badge>
                            <span className="text-white/90 font-medium">
                              You're all caught up!
                            </span>
                          </>
                        )}
                      </motion.div>
                    </div>
                  </div>

                  {/* Enhanced Stats Preview */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                  >
                    <div className="flex justify-between items-center">
                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">TOTAL</div>
                        <div className="text-white text-2xl font-black">{notifications.length}</div>
                      </div>

                      <div className="w-px h-12 bg-white/30" />

                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">UNREAD</div>
                        <div className="text-white text-2xl font-black">{unreadCount}</div>
                      </div>

                      <div className="w-px h-12 bg-white/30" />

                      <div className="text-center">
                        <div className="text-white/80 text-sm font-medium">TODAY</div>
                        <div className="text-white text-2xl font-black">
                          {notifications.filter(n => {
                            const today = new Date();
                            const notifDate = n.timestamp instanceof Date ? n.timestamp : new Date(n.timestamp);
                            return !isNaN(notifDate.getTime()) && notifDate.toDateString() === today.toDateString();
                          }).length}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Enhanced Action Buttons */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    <div className="flex justify-center gap-4">
                      {unreadCount > 0 && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={markAllAsRead}
                          className="flex items-center gap-2 bg-gradient-to-r from-emerald-500/20 to-green-500/20 hover:from-emerald-500/30 hover:to-green-500/30 border-2 border-emerald-400/40 text-emerald-100 px-6 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg shadow-emerald-500/20"
                        >
                          <CheckCheck size={18} />
                          Mark All Read
                        </motion.button>
                      )}

                      {notifications.length > 0 && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleClearAll}
                          className="flex items-center gap-2 bg-gradient-to-r from-rose-500/20 to-red-500/20 hover:from-rose-500/30 hover:to-red-500/30 border-2 border-rose-400/40 text-rose-100 px-6 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg shadow-rose-500/20"
                        >
                          <Trash2 size={18} />
                          Clear All
                        </motion.button>
                      )}
                    </div>
                  </motion.div>
                </div>
              </GlassCard>
            </motion.div>

            {/* EXTREME ENHANCED Filter Section */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, rotateX: -15 }}
              animate={{ opacity: 1, scale: 1, rotateX: 0 }}
              transition={{ delay: 0.2, duration: 0.8, type: "spring", damping: 20 }}
              className="relative z-20"
            >
              <GlassCard
                className="p-8 border-2 border-violet-400/40 overflow-visible shadow-2xl shadow-violet-500/30"
                gradient="from-violet-500/20 via-purple-500/15 to-indigo-500/20"
                hoverEffect={false}
              >
                {/* EXTREME Decorative Background Elements */}
                <div className="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-amber-400/20 via-orange-500/15 to-red-500/10 rounded-full blur-2xl animate-pulse" />
                <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-gradient-to-br from-cyan-400/15 via-blue-500/10 to-indigo-500/8 rounded-full blur-xl" />
                <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-br from-emerald-400/10 to-green-500/8 rounded-full blur-lg animate-bounce" style={{ animationDuration: '3s' }} />

                {/* Floating Sparkles */}
                <motion.div
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute top-4 right-8 text-amber-300/40"
                >
                  <Sparkles size={20} />
                </motion.div>

                <motion.div
                  animate={{
                    rotate: [360, 0],
                    scale: [1, 0.8, 1],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: "linear",
                    delay: 2
                  }}
                  className="absolute bottom-6 left-6 text-violet-300/30"
                >
                  <Crown size={16} />
                </motion.div>

                <div className="relative space-y-6">
                  {/* Enhanced Header with Animation */}
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-4">
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.6, type: 'spring', damping: 15 }}
                        className="relative"
                      >
                        <div className="bg-gradient-to-br from-violet-400/30 via-purple-500/25 to-indigo-500/30 border-2 border-violet-300/50 rounded-2xl p-3 shadow-lg shadow-violet-500/30">
                          <Filter size={24} className="text-violet-100" />
                        </div>

                        {/* Pulsing Ring Effect */}
                        <motion.div
                          animate={{
                            scale: [1, 1.3, 1],
                            opacity: [0.5, 0, 0.5]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="absolute inset-0 border-2 border-violet-400/60 rounded-2xl"
                        />
                      </motion.div>

                      <div className="space-y-1">
                        <motion.h3
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7, duration: 0.6 }}
                          className="text-violet-100 text-2xl font-black bg-gradient-to-r from-violet-200 via-purple-200 to-indigo-200 bg-clip-text text-transparent"
                        >
                          Smart Filter System
                        </motion.h3>
                        <motion.p
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.8, duration: 0.6 }}
                          className="text-violet-300/80 text-sm font-medium"
                        >
                          Organize your notifications with precision
                        </motion.p>
                      </div>
                    </div>

                    {/* Enhanced Filter Stats */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.9, duration: 0.5 }}
                      className="flex items-center gap-3"
                    >
                      <div className="text-center">
                        <div className="text-violet-200/80 text-xs font-semibold">SHOWING</div>
                        <div className="text-violet-100 text-lg font-black">{filteredNotifications.length}</div>
                      </div>
                      <div className="w-px h-8 bg-violet-400/40" />
                      <div className="text-center">
                        <div className="text-violet-200/80 text-xs font-semibold">OF</div>
                        <div className="text-violet-100 text-lg font-black">{notifications.length}</div>
                      </div>
                    </motion.div>
                  </motion.div>

                  {/* EXTREME Enhanced Filter Buttons Grid */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.0, duration: 0.6 }}
                    className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3"
                  >
                    {filterOptions.map((option, index) => {
                      const isSelected = selectedFilter === option.value;
                      const IconComponent = option.value === 'all' ? Target :
                                          option.value === 'order' ? Package :
                                          option.value === 'payment' ? CreditCard :
                                          option.value === 'review' ? Star :
                                          option.value === 'system' ? Settings :
                                          Megaphone;

                      return (
                        <motion.button
                          key={option.value}
                          initial={{ opacity: 0, y: 20, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          transition={{
                            delay: 1.1 + (index * 0.1),
                            duration: 0.5,
                            type: "spring",
                            damping: 15
                          }}
                          whileHover={{
                            scale: 1.05,
                            y: -5,
                            boxShadow: isSelected
                              ? "0 20px 40px -12px rgba(139, 92, 246, 0.6)"
                              : "0 15px 30px -8px rgba(139, 92, 246, 0.3)"
                          }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setSelectedFilter(option.value as any)}
                          className={`relative p-4 rounded-2xl border-2 transition-all duration-500 group ${
                            isSelected
                              ? 'bg-gradient-to-br from-violet-500/40 via-purple-500/35 to-indigo-500/40 border-violet-300/60 shadow-lg shadow-violet-500/40'
                              : 'bg-gradient-to-br from-white/5 via-white/3 to-white/5 border-white/20 hover:border-violet-400/50 hover:bg-gradient-to-br hover:from-violet-500/20 hover:via-purple-500/15 hover:to-indigo-500/20'
                          }`}
                        >
                          {/* Background Glow Effect */}
                          {isSelected && (
                            <motion.div
                              animate={{
                                scale: [1, 1.1, 1],
                                opacity: [0.3, 0.6, 0.3]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                              className="absolute inset-0 bg-gradient-to-br from-violet-400/30 to-purple-500/30 rounded-2xl blur-sm"
                            />
                          )}

                          {/* Content */}
                          <div className="relative space-y-3">
                            {/* Icon with Enhanced Effects */}
                            <motion.div
                              animate={isSelected ? {
                                rotate: [0, 5, -5, 0],
                                scale: [1, 1.1, 1]
                              } : {}}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                              className={`mx-auto w-fit p-2 rounded-xl border ${
                                isSelected
                                  ? 'bg-gradient-to-br from-violet-400/40 to-purple-500/40 border-violet-300/60'
                                  : 'bg-gradient-to-br from-white/10 to-white/5 border-white/30 group-hover:border-violet-400/50'
                              }`}
                            >
                              <IconComponent
                                size={20}
                                className={isSelected ? 'text-violet-100' : 'text-white/70 group-hover:text-violet-200'}
                              />
                            </motion.div>

                            {/* Label */}
                            <div className="text-center space-y-1">
                              <div className={`text-sm font-bold ${
                                isSelected ? 'text-violet-100' : 'text-white/80 group-hover:text-violet-200'
                              }`}>
                                {option.label}
                              </div>

                              {/* Enhanced Count Badge */}
                              <motion.div
                                animate={isSelected && option.count > 0 ? {
                                  scale: [1, 1.1, 1],
                                  opacity: [1, 0.8, 1]
                                } : {}}
                                transition={{
                                  duration: 1.5,
                                  repeat: Infinity,
                                  ease: "easeInOut"
                                }}
                                className={`inline-flex items-center justify-center px-2 py-1 rounded-lg text-xs font-black border ${
                                  isSelected
                                    ? option.count > 0
                                      ? 'bg-gradient-to-r from-amber-400/30 to-orange-500/30 border-amber-300/50 text-amber-100 shadow-lg shadow-amber-500/30'
                                      : 'bg-gradient-to-r from-emerald-400/30 to-green-500/30 border-emerald-300/50 text-emerald-100 shadow-lg shadow-emerald-500/30'
                                    : option.count > 0
                                      ? 'bg-gradient-to-r from-white/15 to-white/10 border-white/30 text-white/90'
                                      : 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 border-gray-400/30 text-gray-300'
                                }`}
                              >
                                {option.count}
                              </motion.div>
                            </div>
                          </div>

                          {/* Selection Indicator */}
                          {isSelected && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute -top-2 -right-2"
                            >
                              <div className="bg-gradient-to-r from-emerald-400 to-green-500 border-2 border-white rounded-full p-1 shadow-lg shadow-emerald-500/50">
                                <Check size={12} className="text-white" />
                              </div>
                            </motion.div>
                          )}

                          {/* Hover Sparkle Effect */}
                          <motion.div
                            className="absolute top-2 right-2 text-violet-300/0 group-hover:text-violet-300/60 transition-all duration-300"
                            animate={{
                              rotate: [0, 180, 360],
                              scale: [1, 1.2, 1]
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              ease: "linear"
                            }}
                          >
                            <Zap size={12} />
                          </motion.div>
                        </motion.button>
                      );
                    })}
                  </motion.div>

                  {/* Enhanced Active Filter Display */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1.8, duration: 0.5 }}
                    className="flex items-center justify-center"
                  >
                    <div className="bg-gradient-to-r from-violet-500/20 via-purple-500/15 to-indigo-500/20 border border-violet-400/40 rounded-2xl px-6 py-3 shadow-lg shadow-violet-500/20">
                      <div className="flex items-center gap-3">
                        <motion.div
                          animate={{
                            scale: [1, 1.1, 1],
                            opacity: [0.7, 1, 0.7]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="w-2 h-2 bg-gradient-to-r from-violet-400 to-purple-500 rounded-full"
                        />
                        <span className="text-violet-100 font-semibold">
                          Currently showing: <span className="font-black text-violet-200">
                            {filterOptions.find(opt => opt.value === selectedFilter)?.label}
                          </span>
                        </span>
                        <Badge variant="default" className="bg-gradient-to-r from-violet-500/30 to-purple-500/30 border-violet-400/50">
                          {filteredNotifications.length} items
                        </Badge>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </GlassCard>
            </motion.div>

            {/* EXTREME ENHANCED Notifications List */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.0, duration: 0.8 }}
              className="space-y-6"
            >
              {/* List Header */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 2.1, duration: 0.6 }}
              >
                <GlassCard
                  className="p-6 border-slate-400/30"
                  gradient="from-slate-500/10 via-gray-500/8 to-zinc-500/10"
                  hoverEffect={false}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <motion.div
                        animate={{
                          rotate: [0, 10, -10, 0],
                          scale: [1, 1.1, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="bg-gradient-to-br from-slate-400/20 to-gray-500/20 rounded-2xl p-3 border border-slate-400/30"
                      >
                        <BellRing size={24} className="text-slate-300" />
                      </motion.div>
                      <div>
                        <h3 className="text-slate-200 text-xl font-black">
                          {selectedFilter === 'all' ? 'All Notifications' : `${filterOptions.find(opt => opt.value === selectedFilter)?.label} Notifications`}
                        </h3>
                        <p className="text-slate-400 text-sm">
                          {filteredNotifications.length} {filteredNotifications.length === 1 ? 'notification' : 'notifications'} found
                        </p>
                      </div>
                    </div>

                    {/* Sort Options */}
                    <div className="flex items-center gap-2">
                      <span className="text-slate-400 text-sm">Sorted by:</span>
                      <Badge variant="default" className="bg-gradient-to-r from-slate-500/20 to-gray-500/20 border-slate-400/40">
                        Latest First
                      </Badge>
                    </div>
                  </div>
                </GlassCard>
              </motion.div>

              {/* Enhanced Notifications Grid */}
              <div className="space-y-4">
                <AnimatePresence mode="popLayout">
                  {filteredNotifications.length > 0 ? (
                    filteredNotifications.map((notification, index) => (
                      <motion.div
                        key={notification.id}
                        layout
                        initial={{ opacity: 0, x: -50, scale: 0.9 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        exit={{
                          opacity: 0,
                          x: 100,
                          scale: 0.8,
                          transition: { duration: 0.3 }
                        }}
                        transition={{
                          delay: 2.2 + (index * 0.1),
                          duration: 0.6,
                          type: "spring",
                          damping: 20
                        }}
                        whileHover={{
                          scale: 1.02,
                          y: -5,
                          transition: { duration: 0.2 }
                        }}
                      >
                        <GlassCard
                          className={`p-8 transition-all duration-500 group relative overflow-hidden ${
                            notification.read
                              ? 'border-slate-400/30 bg-gradient-to-br from-slate-500/8 to-gray-500/8'
                              : `border-2 ${getNotificationBorderColor(notification.type)} bg-gradient-to-br ${getNotificationColor(notification.type)}/20 shadow-2xl`
                          }`}
                          hoverEffect={false}
                          onClick={() => handleNotificationPress(notification)}
                        >
                          {/* Enhanced Background Effects */}
                          {!notification.read && (
                            <>
                              <motion.div
                                animate={{
                                  scale: [1, 1.2, 1],
                                  opacity: [0.1, 0.3, 0.1]
                                }}
                                transition={{
                                  duration: 3,
                                  repeat: Infinity,
                                  ease: "easeInOut"
                                }}
                                className={`absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br ${getNotificationColor(notification.type)} rounded-full blur-2xl`}
                              />
                              <motion.div
                                animate={{
                                  scale: [1.2, 1, 1.2],
                                  opacity: [0.05, 0.15, 0.05]
                                }}
                                transition={{
                                  duration: 4,
                                  repeat: Infinity,
                                  ease: "easeInOut",
                                  delay: 1
                                }}
                                className={`absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br ${getNotificationColor(notification.type)} rounded-full blur-xl`}
                              />
                            </>
                          )}

                          {/* Floating Sparkles for Unread */}
                          {!notification.read && (
                            <motion.div
                              animate={{
                                rotate: [0, 360],
                                scale: [1, 1.2, 1],
                                opacity: [0.4, 0.8, 0.4]
                              }}
                              transition={{
                                duration: 6,
                                repeat: Infinity,
                                ease: "linear"
                              }}
                              className="absolute top-4 right-4 text-amber-300/60"
                            >
                              <Sparkles size={16} />
                            </motion.div>
                          )}

                          <div className="relative flex items-start gap-6">
                            {/* EXTREME Enhanced Icon */}
                            <motion.div
                              initial={{ scale: 0.5, opacity: 0, rotate: -180 }}
                              animate={{ scale: 1, opacity: 1, rotate: 0 }}
                              transition={{
                                delay: 2.3 + (index * 0.05),
                                type: 'spring',
                                damping: 15,
                                duration: 0.8
                              }}
                              whileHover={{
                                scale: 1.1,
                                rotate: 5,
                                transition: { duration: 0.2 }
                              }}
                              className="relative"
                            >
                              <div className={`bg-gradient-to-br ${getNotificationColor(notification.type)} p-4 rounded-3xl border-2 shadow-2xl ${
                                notification.read
                                  ? 'border-white/20 opacity-70'
                                  : 'border-white/50 shadow-lg'
                              } ${!notification.read ? `shadow-${notification.type === 'order' ? 'emerald' : notification.type === 'payment' ? 'blue' : notification.type === 'system' ? 'orange' : notification.type === 'review' ? 'violet' : 'rose'}-500/40` : ''}`}>
                                {React.createElement(getNotificationIcon(notification.type), {
                                  size: 28,
                                  className: "text-white"
                                })}
                              </div>

                              {/* Pulsing Ring for Unread */}
                              {!notification.read && (
                                <motion.div
                                  animate={{
                                    scale: [1, 1.4, 1],
                                    opacity: [0.6, 0, 0.6]
                                  }}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                  }}
                                  className={`absolute inset-0 border-2 ${getNotificationBorderColor(notification.type)} rounded-3xl`}
                                />
                              )}

                              {/* Priority Crown */}
                              {notification.priority === 'high' && !notification.read && (
                                <motion.div
                                  animate={{
                                    rotate: [0, 10, -10, 0],
                                    scale: [1, 1.1, 1]
                                  }}
                                  transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                  }}
                                  className="absolute -top-2 -right-2"
                                >
                                  <div className="bg-gradient-to-r from-amber-400 to-orange-500 border-2 border-white rounded-full p-1 shadow-lg shadow-amber-500/50">
                                    <Crown size={12} className="text-white" />
                                  </div>
                                </motion.div>
                              )}
                            </motion.div>

                            {/* Enhanced Content */}
                            <div className="flex-1 space-y-4">
                              <div className="flex items-start justify-between">
                                <div className="space-y-2">
                                  <motion.h4
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 2.4 + (index * 0.05), duration: 0.5 }}
                                    className={`text-lg font-black ${
                                      notification.read
                                        ? 'text-slate-300'
                                        : `${getNotificationAccentColor(notification.type)} text-shadow-sm`
                                    }`}
                                  >
                                    {notification.title}
                                  </motion.h4>

                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 2.5 + (index * 0.05), duration: 0.5 }}
                                    className="flex items-center gap-3"
                                  >
                                    {/* Type Badge */}
                                    <Badge
                                      variant={notification.type === 'order' ? 'success' :
                                              notification.type === 'payment' ? 'default' :
                                              notification.type === 'system' ? 'warning' : 'default'}
                                      className="capitalize"
                                    >
                                      {notification.type}
                                    </Badge>

                                    {/* Priority Badge */}
                                    <Badge
                                      variant={notification.priority === 'high' ? 'error' :
                                              notification.priority === 'medium' ? 'warning' : 'success'}
                                      className="capitalize"
                                    >
                                      {notification.priority} Priority
                                    </Badge>

                                    {/* Time Badge */}
                                    <div className="flex items-center gap-1 text-white/50 text-xs">
                                      <Clock size={12} />
                                      {formatTimeAgo(notification.timestamp)}
                                    </div>
                                  </motion.div>
                                </div>

                                <div className="flex items-center gap-3">
                                  {/* Enhanced Priority Indicator */}
                                  <motion.div
                                    className={`w-4 h-4 rounded-full ${getPriorityColor(notification.priority)}`}
                                    animate={notification.priority === 'high' && !notification.read ? {
                                      scale: [1, 1.3, 1],
                                      opacity: [1, 0.7, 1]
                                    } : {}}
                                    transition={{
                                      duration: 1.5,
                                      repeat: Infinity,
                                      ease: "easeInOut"
                                    }}
                                  />

                                  {/* Enhanced Delete Button */}
                                  <motion.button
                                    whileHover={{
                                      scale: 1.2,
                                      rotate: 90,
                                      backgroundColor: "rgba(239, 68, 68, 0.2)"
                                    }}
                                    whileTap={{ scale: 0.9 }}
                                    onClick={(e) => handleDeleteNotification(notification.id, e)}
                                    className="p-2 rounded-full border border-white/20 hover:border-red-400/50 transition-all duration-300 group"
                                  >
                                    <X size={18} className="text-white/60 group-hover:text-red-300 transition-colors duration-300" />
                                  </motion.button>
                                </div>
                              </div>

                              <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 2.6 + (index * 0.05), duration: 0.5 }}
                                className={`text-base leading-relaxed ${
                                  notification.read ? 'text-white/60' : 'text-white/90'
                                }`}
                              >
                                {notification.message}
                              </motion.p>

                              <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 2.7 + (index * 0.05), duration: 0.5 }}
                                className="flex items-center justify-between pt-2"
                              >
                                <div className="flex items-center gap-4">
                                  {/* Order ID if available */}
                                  {notification.orderId && (
                                    <div className="flex items-center gap-2">
                                      <span className="text-white/50 text-sm">Order:</span>
                                      <Badge variant="default" className="font-mono">
                                        #{notification.orderId}
                                      </Badge>
                                    </div>
                                  )}
                                </div>

                                {!notification.read && (
                                  <motion.div
                                    animate={{
                                      scale: [1, 1.1, 1],
                                      opacity: [1, 0.8, 1]
                                    }}
                                    transition={{
                                      duration: 2,
                                      repeat: Infinity,
                                      ease: "easeInOut"
                                    }}
                                  >
                                    <Badge variant="error" className="bg-gradient-to-r from-red-500/30 to-rose-500/30 border-red-400/50 shadow-lg shadow-red-500/30">
                                      <motion.div
                                        animate={{
                                          scale: [1, 1.2, 1]
                                        }}
                                        transition={{
                                          duration: 1,
                                          repeat: Infinity,
                                          ease: "easeInOut"
                                        }}
                                        className="w-2 h-2 bg-red-400 rounded-full mr-2"
                                      />
                                      NEW
                                    </Badge>
                                  </motion.div>
                                )}
                              </motion.div>
                            </div>
                          </div>

                          {/* Click Indicator */}
                          <motion.div
                            className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                            animate={{
                              x: [0, 5, 0],
                            }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          >
                            <div className="flex items-center gap-2 text-white/60 text-sm">
                              <span>Click to view</span>
                              <motion.div
                                animate={{ x: [0, 3, 0] }}
                                transition={{ duration: 1, repeat: Infinity }}
                              >
                                →
                              </motion.div>
                            </div>
                          </motion.div>
                        </GlassCard>
                      </motion.div>
                    ))
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      <GlassCard
                        className="p-16 text-center border-slate-400/30"
                        gradient="from-slate-500/10 via-gray-500/8 to-zinc-500/10"
                        hoverEffect={false}
                      >
                        <div className="space-y-6">
                          <motion.div
                            className="bg-gradient-to-br from-slate-400/20 to-gray-500/20 rounded-3xl p-6 w-24 h-24 mx-auto flex items-center justify-center border border-slate-400/30"
                            animate={{
                              scale: [1, 1.1, 1],
                              opacity: [0.7, 1, 0.7]
                            }}
                            transition={{
                              duration: 3,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          >
                            <BellRing size={40} className="text-slate-300" />
                          </motion.div>
                          <div className="space-y-3">
                            <h4 className="text-slate-200 text-2xl font-black">No Notifications Found</h4>
                            <p className="text-slate-400 text-lg">
                              {selectedFilter === 'all'
                                ? "You're all caught up! No new notifications to display."
                                : `No ${filterOptions.find(opt => opt.value === selectedFilter)?.label.toLowerCase()} notifications found.`
                              }
                            </p>
                            <motion.div
                              animate={{
                                scale: [1, 1.05, 1],
                                opacity: [0.5, 0.8, 0.5]
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut"
                              }}
                            >
                              <Badge variant="success" className="mt-4">
                                ✨ All Clear
                              </Badge>
                            </motion.div>
                          </div>
                        </div>
                      </GlassCard>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotificationsPage;
