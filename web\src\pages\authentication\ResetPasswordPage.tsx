import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Lock, ArrowLeft, AlertCircle, CheckCircle, Eye, EyeOff, Sparkles,
  Shield, ChevronRight, Key, Zap
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { apiService } from '../../services/api';

interface ResetPasswordFormData {
  code: string;
  newPassword: string;
  confirmPassword: string;
}

const ResetPasswordPage: React.FC = () => {
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState<'initial' | 'welcome' | 'form'>('initial');

  const location = useLocation();
  const navigate = useNavigate();
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const email = location.state?.email || '';

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    defaultValues: {
      code: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  const watchedPassword = watch('newPassword');

  useEffect(() => {
    if (!email) {
      navigate('/auth/verify-before-reset');
    }
  }, [email, navigate]);

  // Enhanced animation sequence
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationPhase('welcome'), 800);
    const timer2 = setTimeout(() => setAnimationPhase('form'), 1600);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  const handleCodeChange = (value: string, index: number) => {
    if (value.length > 1) return;

    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);
    setError('');

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const onSubmit = async (data: ResetPasswordFormData) => {
    const code = verificationCode.join('');

    if (code.length !== 6) {
      setError('Please enter the complete 6-digit code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // First verify the reset code
      const verifyResponse = await apiService.verifyResetCode(email, code);

      if (!verifyResponse.success) {
        setError(verifyResponse.message || 'Invalid reset code');
        setIsLoading(false);
        return;
      }

      // Then reset the password
      const resetResponse = await apiService.resetPassword({
        email,
        code,
        newPassword: data.newPassword,
      });

      if (resetResponse.success) {
        setSuccess('Password reset successfully! Redirecting to login...');
        setTimeout(() => {
          navigate('/auth/login');
        }, 2000);
      } else {
        setError(resetResponse.message || 'Failed to reset password. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!email) {
    return null;
  }

  return (
    <div className="relative">
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -bottom-20 -right-20 w-32 h-32 bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, -30, -10],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Security icons floating */}
        <motion.div
          className="absolute opacity-5"
          style={{ left: '15%', top: '25%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 0,
            ease: "easeInOut"
          }}
        >
          <Shield size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '85%', top: '75%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 2,
            ease: "easeInOut"
          }}
        >
          <Key size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '10%', top: '85%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 4,
            ease: "easeInOut"
          }}
        >
          <Lock size={24} className="text-primary-400" />
        </motion.div>
        <motion.div
          className="absolute opacity-5"
          style={{ left: '90%', top: '15%' }}
          animate={{
            y: [-5, 5, -5],
            rotate: [-5, 5, -5],
            opacity: [0.05, 0.15, 0.05],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            delay: 6,
            ease: "easeInOut"
          }}
        >
          <Zap size={24} className="text-primary-400" />
        </motion.div>
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10"
      >
        {/* Enhanced Welcome Header */}
        <AnimatePresence mode="wait">
          {animationPhase === 'initial' && (
            <motion.div
              key="initial"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  rotate: [0, 2, -2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                <Sparkles size={48} className="text-primary-500 mx-auto mb-2" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700">Securing Your Account...</h3>
            </motion.div>
          )}

          {animationPhase === 'welcome' && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
                className="inline-block mb-4"
              >
                <div className="relative">
                  <Lock size={56} className="text-primary-500 mx-auto" />
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                  />
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent"
              >
                Secure Password Reset 🔐
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-gray-600 mt-2"
              >
                Almost there! Let's create your new secure password
              </motion.p>
            </motion.div>
          )}

          {animationPhase === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Premium Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Lock size={32} className="text-primary-500" />
                  </motion.div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent">
                    Reset Password
                  </h2>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    <Shield size={28} className="text-secondary-500" />
                  </motion.div>
                </div>
                <p className="text-gray-600 font-medium">
                  Enter verification code and create your new password
                </p>
              </motion.div>

              {/* Enhanced Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl flex items-center space-x-3 text-red-700 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertCircle size={20} className="text-red-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{error}</span>
                </motion.div>
              )}

              {/* Enhanced Success Display */}
              {success && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl flex items-center space-x-3 text-green-700 shadow-lg"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.5 }}
                  >
                    <CheckCircle size={20} className="text-green-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{success}</span>
                </motion.div>
              )}

              {/* Premium Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Enhanced Verification Code Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="relative"
                >
                  <div className="text-center mb-4">
                    <motion.div
                      animate={{
                        scale: [1, 1.05, 1],
                        rotate: [0, 2, -2, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="inline-block mb-2"
                    >
                      <Key size={24} className="text-primary-500" />
                    </motion.div>
                    <label className="block text-lg font-bold text-gray-800 mb-2">
                      Verification Code
                    </label>
                    <p className="text-sm text-gray-600 mb-4">
                      Enter the 6-digit code sent to your email
                    </p>
                  </div>

                  <div className="flex justify-center space-x-3 mb-6">
                    {verificationCode.map((digit, index) => (
                      <motion.input
                        key={index}
                        ref={el => { inputRefs.current[index] = el; }}
                        type="text"
                        maxLength={1}
                        value={digit}
                        onChange={e => handleCodeChange(e.target.value, index)}
                        onKeyDown={e => handleKeyDown(e, index)}
                        autoComplete="one-time-code"
                        className="w-14 h-14 text-center text-xl font-bold border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white/80 backdrop-blur-sm hover:border-primary-300 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileFocus={{ scale: 1.05 }}
                        whileHover={{ scale: 1.02 }}
                      />
                    ))}
                  </div>
                </motion.div>

                {/* Enhanced New Password Field */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="relative"
                >
                  <div className="relative group">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                        focusedField === 'newPassword' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                      }`}
                    />
                    <div className="relative">
                      <Input
                        label="New Password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your new password"
                        icon={<Lock size={18} className="text-primary-500" />}
                        error={errors.newPassword?.message}
                        onFocus={() => setFocusedField('newPassword')}
                        className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                        autoComplete="new-password"
                        {...register('newPassword', {
                          required: 'Password is required',
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters',
                          },
                          onBlur: () => setFocusedField(null)
                        })}
                      />
                      <motion.button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-4 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                      </motion.button>
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced Confirm Password Field */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                  className="relative"
                >
                  <div className="relative group">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                        focusedField === 'confirmPassword' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                      }`}
                    />
                    <div className="relative">
                      <Input
                        label="Confirm New Password"
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="Confirm your new password"
                        icon={<Lock size={18} className="text-secondary-500" />}
                        error={errors.confirmPassword?.message}
                        onFocus={() => setFocusedField('confirmPassword')}
                        className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                        autoComplete="new-password"
                        {...register('confirmPassword', {
                          required: 'Please confirm your password',
                          validate: value =>
                            value === watchedPassword || 'Passwords do not match',
                          onBlur: () => setFocusedField(null)
                        })}
                      />
                      <motion.button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-4 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                      </motion.button>
                    </div>
                  </div>
                </motion.div>

                {/* Password Strength Indicator */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-4 border border-gray-200"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Shield size={16} className="text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Password Security</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${watchedPassword && watchedPassword.length >= 6 ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>6+ characters</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${watchedPassword && /[A-Z]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Uppercase letter</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${watchedPassword && /[0-9]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Number</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${watchedPassword && /[!@#$%^&*]/.test(watchedPassword) ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Special character</span>
                    </div>
                  </div>
                </motion.div>

                {/* Premium Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="relative"
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-30"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isLoading}
                    disabled={isLoading || verificationCode.join('').length !== 6}
                    className="w-full relative z-10 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                  >
                    <div className="flex items-center justify-center gap-3">
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Lock size={20} />
                        </motion.div>
                      ) : (
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Lock size={20} />
                        </motion.div>
                      )}
                      <span className="text-lg">
                        {isLoading ? 'Resetting Password...' : 'Reset Password'}
                      </span>
                    </div>
                  </Button>
                </motion.div>
              </form>

              {/* Enhanced Back Navigation */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-8 text-center"
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500 font-medium">Need to go back?</span>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/auth/verify-before-reset"
                    className="group inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-all duration-300 bg-gradient-to-r from-primary-50 to-secondary-50 hover:from-primary-100 hover:to-secondary-100 px-6 py-3 rounded-2xl border border-primary-200 hover:border-primary-300"
                  >
                    <ArrowLeft size={16} />
                    <span>Back to Email Verification</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, -3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={16} className="rotate-180" />
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default ResetPasswordPage;
