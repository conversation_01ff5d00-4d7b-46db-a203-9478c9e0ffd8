import { ScrollView, Alert } from 'react-native';
import { Button, Input, Label, Text, View, YStack, Card, Separator, XStack } from 'tamagui';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore, useUpdateRequestPickup } from './useRequestPickupStore';
import { requestPickup } from '../../../services/apiService';

export function RequestPickupForm() {
  const router = useRouter();
  const {
    pickup,
    itemDescription,
    preferredTime,
    notes,
    updateField,
    reset
  } = useUpdateRequestPickup();
  const { addPickupRequest } = useRequestPickupStore();

  const isFormValid = () =>
    pickup && itemDescription.trim() && preferredTime.trim();

  return (
    <>
      {/* Header */}
      <View
        style={{
          width: '100%',
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$8" fontWeight="700" color="white" textAlign="center">
            📥 Request Pickup
          </Text>
        </MotiView>
      </View>

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
        <YStack gap="$4" p="$4">
          {/* Pickup Location */}
          <Card padded elevation="$2" bordered>
            <Text fontWeight="600" fontSize="$6" mb="$2">
              📍 Pickup Location  <Text color="red">*</Text>
            </Text>

            <XStack gap="$2">
              <Input
                flex={1}
                value={pickup?.address}
                placeholder="Set on map"
                editable={false}
                onChangeText={(text) => updateField('pickup', {address: text})} //temporarly
                size="$4"
                borderRadius="$4"
              />
              <Button
                size="$4"
                onPress={() => router.push('/home/<USER>')}
              >
                Set
              </Button>
            </XStack>
          </Card>

          {/* Item Info */}
          <Card padded elevation="$2" bordered>
            <Text fontWeight="600" fontSize="$6" mb="$2">
              📦 Item Details
            </Text>
            <YStack gap="$3">
              <Label>Description <Text color="red">*</Text></Label>
              <Input
                placeholder="e.g. A small package of books"
                value={itemDescription}
                onChangeText={(text) => updateField('itemDescription', text)}
                borderRadius="$4"
                size="$4"
              />

              <Label>Preferred Pickup Time <Text color="red">*</Text></Label>
              <Input
                placeholder="e.g. Today at 4 PM"
                value={preferredTime}
                onChangeText={(text) => updateField('preferredTime', text)}
                borderRadius="$4"
                size="$4"
              />

              <Label>Notes (optional)</Label>
              <Input
                width={"100%"}
                placeholder="Optional instructions for the driver"
                value={notes}
                onChangeText={(text) => updateField('notes', text)}
                borderRadius="$4"
                multiline
                numberOfLines={3}
                size="$4"
              />
            </YStack>
          </Card>

          <Separator />

          {/* Confirm Button */}
          <Button
            width={"100%"}
            height={"$5"}
            size="$9"
            bg="$primary"
            color="white"
            br="$10"
            fontSize={"$6"}
            icon={<Ionicons name="mail-open-outline" size={25} color="white" />}
            disabled={!isFormValid()}
            opacity={isFormValid() ? 1 : 0.5}
            onPress={async () => {
              try {
                // Prepare pickup data for backend
                const pickupData = {
                  pickupAddress: {
                    street: pickup?.address || '',
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: pickup?.lat || 32.2211,
                      lng: pickup?.lng || 35.2544
                    }
                  },
                  packageDetails: {
                    type: itemDescription,
                    size: 'medium' as const, // Default size
                    weight: '1kg' // Default weight
                  },
                  preferredTime,
                  notes
                };

                // Create pickup request via backend API
                const createdPickup = await requestPickup(pickupData);

                // Add to local store for immediate UI update
                addPickupRequest({
                  id: createdPickup.trackingNumber,
                  pickup,
                  itemDescription,
                  preferredTime,
                  notes,
                  driverName: 'Ali Alaa', // temporary
                  driverPhone: '0595959595',
                  status: 'pending',
                  createdAt: new Date().toISOString(),
                  estimatedTime: '45-60 mins',
                  cost: createdPickup.cost
                });

                reset();
                router.push('/home/<USER>');
              } catch (error) {
                console.error('Error creating pickup request:', error);
                Alert.alert('Error', 'Failed to create pickup request. Please try again.');
              }
            }}
            pressStyle={{ bg: "$third" }}
          >
              Confirm Request
          </Button>
        </YStack>
      </ScrollView>
    </>
  );
}
