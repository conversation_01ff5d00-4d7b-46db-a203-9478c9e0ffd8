const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  category: String,
  products: [{
    id: String,
    name: String,
    image: String,
    price: Number,
    category: String,
    isAvailable: Boolean
  }]
});

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function testProducts() {
  try {
    await connectDB();

    // Find the "3a kefak" supplier
    const supplier = await Supplier.findOne({ 
      $or: [
        { name: /3a kefak/i },
        { name: /كيفك/i },
        { id: '3a-kefak' }
      ]
    });

    if (!supplier) {
      console.log('❌ Supplier "3a kefak" not found');
      process.exit(1);
    }

    console.log(`✅ Found supplier: ${supplier.name} (ID: ${supplier.id})`);
    console.log(`📦 Products count: ${supplier.products.length}`);
    
    if (supplier.products.length > 0) {
      console.log('\n📋 Products list:');
      supplier.products.forEach((product, index) => {
        console.log(`  ${index + 1}. ${product.name}`);
        console.log(`     - ID: ${product.id}`);
        console.log(`     - Category: ${product.category}`);
        console.log(`     - Price: ₪${product.price}`);
        console.log(`     - Available: ${product.isAvailable}`);
        console.log(`     - Image: ${product.image ? 'Yes' : 'No'}`);
        console.log('');
      });
    } else {
      console.log('❌ No products found for this supplier');
    }

    // Test the categories
    const categories = ['All', ...new Set(supplier.products.map(p => p.category))];
    console.log('📂 Available categories:', categories);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing products:', error);
    process.exit(1);
  }
}

// Run the test
testProducts();
