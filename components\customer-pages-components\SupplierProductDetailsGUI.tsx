import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { RestaurantProductDetails } from './product-details/RestaurantProductDetails';
import { ClothingProductDetails } from './product-details/ClothingProductDetails';
import { YStack, Text } from 'tamagui';

type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    category: string;
}

export function SupplierProductDetailsGUI({ category }: SupplierDetailsGUIProps) {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Extract category from params if not provided as prop
  const supplierCategory = category || (Array.isArray(params.category) ? params.category[0] : params.category) || 'restaurants';

  let product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  } | undefined = undefined;

  // Safely parse product JSON
  try {
    if (params.product) {
      product = JSON.parse(params.product as string);
    }
  } catch (error) {
    console.error('Error parsing product data:', error);
    console.log('Product param:', params.product);
  }

  const title = product?.name ?? 'Item';

  const supplierId = Array.isArray(params.supplierId) ? params.supplierId[0] : params.supplierId;
  const supplierName = Array.isArray(params.supplierName) ? params.supplierName[0] : params.supplierName;

  return (
    <>
      <Stack.Screen options={{ title, headerShown: true }} />
      {product ? (supplierCategory === 'restaurants' ? (
        <RestaurantProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      ) : (
        <ClothingProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      )) : (
        <YStack flex={1} justifyContent="center" alignItems="center" gap="$4" padding="$4">
          <Text fontSize="$5" color="$gray10">Product not found</Text>
          <Text fontSize="$3" color="$gray8" textAlign="center">
            The product data could not be loaded. Please try again.
          </Text>
        </YStack>
      )}
    </>
  )
}