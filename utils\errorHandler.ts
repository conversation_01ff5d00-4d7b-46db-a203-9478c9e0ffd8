import { TFunction } from 'i18next';

export interface ErrorResponse {
  success: false;
  message: string;
  error?: string;
  statusCode?: number;
}

export interface UserFriendlyError {
  title: string;
  message: string;
  type: 'error' | 'warning' | 'info';
}

/**
 * Maps backend error messages to user-friendly messages
 */
export const getErrorMessage = (error: any, t: TFunction): UserFriendlyError => {
  // Default error
  let title = t('errors.general.title', { defaultValue: 'Something went wrong' });
  let message = t('errors.general.message', { defaultValue: 'Please try again later' });
  let type: 'error' | 'warning' | 'info' = 'error';

  // Extract error message from different error formats
  let errorMessage = '';
  if (typeof error === 'string') {
    errorMessage = error;
  } else if (error?.error) {
    // Prioritize the detailed error message from API responses
    errorMessage = error.error;
  } else if (error?.response?.error) {
    // Check for error in the response object
    errorMessage = error.response.error;
  } else if (error?.message) {
    errorMessage = error.message;
  } else if (error?.response?.message) {
    // Check for message in the response object
    errorMessage = error.response.message;
  }

  // Convert to lowercase for easier matching
  const lowerErrorMessage = errorMessage.toLowerCase();

  // Validation errors
  if (lowerErrorMessage.includes('validation failed')) {
    title = t('errors.validation.title', { defaultValue: 'Invalid Information' });
    // Use the specific validation error if available
    const specificError = error?.error || error?.message;
    message = specificError || t('errors.validation.message', { defaultValue: 'Please check your input and try again.' });
  }
  // Authentication errors
  else if (lowerErrorMessage.includes('invalid email or password') ||
      lowerErrorMessage.includes('invalid credentials') ||
      lowerErrorMessage.includes('authentication failed')) {
    title = t('errors.auth.invalidCredentials.title', { defaultValue: 'Login Failed' });
    message = t('errors.auth.invalidCredentials.message', { defaultValue: 'The email or password you entered is incorrect. Please check and try again.' });
  }
  
  // Email already exists
  else if (lowerErrorMessage.includes('email already exists') || 
           lowerErrorMessage.includes('email already registered') ||
           lowerErrorMessage.includes('user already exists')) {
    title = t('errors.auth.emailExists.title', { defaultValue: 'Email Already Registered' });
    message = t('errors.auth.emailExists.message', { defaultValue: 'An account with this email already exists. Please use a different email or try logging in.' });
  }
  
  // Username already exists
  else if (lowerErrorMessage.includes('username already exists') || 
           lowerErrorMessage.includes('username already taken')) {
    title = t('errors.auth.usernameExists.title', { defaultValue: 'Username Not Available' });
    message = t('errors.auth.usernameExists.message', { defaultValue: 'This username is already taken. Please choose a different username.' });
  }
  
  // Phone number already exists
  else if (lowerErrorMessage.includes('phone') && lowerErrorMessage.includes('already exists')) {
    title = t('errors.auth.phoneExists.title', { defaultValue: 'Phone Number Already Registered' });
    message = t('errors.auth.phoneExists.message', { defaultValue: 'This phone number is already associated with another account.' });
  }
  
  // Weak password
  else if (lowerErrorMessage.includes('password') && 
           (lowerErrorMessage.includes('weak') || lowerErrorMessage.includes('requirements') || 
            lowerErrorMessage.includes('minimum') || lowerErrorMessage.includes('characters'))) {
    title = t('errors.auth.weakPassword.title', { defaultValue: 'Password Too Weak' });
    message = t('errors.auth.weakPassword.message', { defaultValue: 'Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.' });
  }
  
  // Invalid email format
  else if (lowerErrorMessage.includes('invalid email') || lowerErrorMessage.includes('email format')) {
    title = t('errors.auth.invalidEmail.title', { defaultValue: 'Invalid Email' });
    message = t('errors.auth.invalidEmail.message', { defaultValue: 'Please enter a valid email address.' });
  }
  
  // Account not verified
  else if (lowerErrorMessage.includes('not verified') || lowerErrorMessage.includes('verify your email')) {
    title = t('errors.auth.notVerified.title', { defaultValue: 'Account Not Verified' });
    message = t('errors.auth.notVerified.message', { defaultValue: 'Please verify your email address before logging in. Check your inbox for the verification link.' });
    type = 'warning';
  }
  
  // Account deactivated
  else if (lowerErrorMessage.includes('deactivated') || lowerErrorMessage.includes('suspended')) {
    title = t('errors.auth.accountDeactivated.title', { defaultValue: 'Account Deactivated' });
    message = t('errors.auth.accountDeactivated.message', { defaultValue: 'Your account has been deactivated. Please contact support for assistance.' });
  }
  
  // Network errors
  else if (lowerErrorMessage.includes('network') || lowerErrorMessage.includes('connection') || 
           lowerErrorMessage.includes('timeout') || lowerErrorMessage.includes('fetch')) {
    title = t('errors.network.title', { defaultValue: 'Connection Problem' });
    message = t('errors.network.message', { defaultValue: 'Please check your internet connection and try again.' });
  }
  
  // Server errors (5xx)
  else if (lowerErrorMessage.includes('server error') || lowerErrorMessage.includes('internal error') ||
           lowerErrorMessage.includes('500') || lowerErrorMessage.includes('503')) {
    title = t('errors.server.title', { defaultValue: 'Server Temporarily Unavailable' });
    message = t('errors.server.message', { defaultValue: 'Our servers are experiencing issues. Please try again in a few minutes.' });
  }
  
  // Validation errors
  else if (lowerErrorMessage.includes('required') || lowerErrorMessage.includes('missing')) {
    title = t('errors.validation.title', { defaultValue: 'Missing Information' });
    message = t('errors.validation.message', { defaultValue: 'Please fill in all required fields and try again.' });
  }
  
  // Rate limiting
  else if (lowerErrorMessage.includes('too many') || lowerErrorMessage.includes('rate limit')) {
    title = t('errors.rateLimit.title', { defaultValue: 'Too Many Attempts' });
    message = t('errors.rateLimit.message', { defaultValue: 'You\'ve made too many attempts. Please wait a few minutes before trying again.' });
    type = 'warning';
  }

  return { title, message, type };
};

/**
 * Maps HTTP status codes to user-friendly messages
 */
export const getErrorFromStatusCode = (statusCode: number, t: TFunction): UserFriendlyError => {
  switch (statusCode) {
    case 400:
      return {
        title: t('errors.badRequest.title', { defaultValue: 'Invalid Request' }),
        message: t('errors.badRequest.message', { defaultValue: 'The information provided is invalid. Please check and try again.' }),
        type: 'error'
      };
    case 401:
      return {
        title: t('errors.unauthorized.title', { defaultValue: 'Authentication Required' }),
        message: t('errors.unauthorized.message', { defaultValue: 'Please log in to continue.' }),
        type: 'warning'
      };
    case 403:
      return {
        title: t('errors.forbidden.title', { defaultValue: 'Access Denied' }),
        message: t('errors.forbidden.message', { defaultValue: 'You don\'t have permission to perform this action.' }),
        type: 'error'
      };
    case 404:
      return {
        title: t('errors.notFound.title', { defaultValue: 'Not Found' }),
        message: t('errors.notFound.message', { defaultValue: 'The requested resource could not be found.' }),
        type: 'error'
      };
    case 429:
      return {
        title: t('errors.rateLimit.title', { defaultValue: 'Too Many Attempts' }),
        message: t('errors.rateLimit.message', { defaultValue: 'You\'ve made too many attempts. Please wait a few minutes before trying again.' }),
        type: 'warning'
      };
    case 500:
    case 502:
    case 503:
    case 504:
      return {
        title: t('errors.server.title', { defaultValue: 'Server Error' }),
        message: t('errors.server.message', { defaultValue: 'Our servers are experiencing issues. Please try again later.' }),
        type: 'error'
      };
    default:
      return {
        title: t('errors.general.title', { defaultValue: 'Something went wrong' }),
        message: t('errors.general.message', { defaultValue: 'An unexpected error occurred. Please try again.' }),
        type: 'error'
      };
  }
};
