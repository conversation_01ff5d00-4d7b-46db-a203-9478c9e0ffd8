import { create } from 'zustand';
import { apiService } from '../../services/apiService';

export interface SignupData {
  // Step 1: Basic Info
  firstName: string;
  lastName: string;
  email: string;
  
  // Step 2: Contact & Security
  phone: string;
  password: string;
  confirmPassword: string;
  
  // Step 3: Profile Setup
  username: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other' | '';
  
  // Step 4: Address Info
  address: string;
  city: string;
  country: string;
  
  // Step 5: Business Info (for suppliers only)
  userType: 'customer' | 'supplier';
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;
  
  // Step 6: Location & Preferences
  location?: [number, number]; // [lng, lat]
  notifications: boolean;
  terms: boolean;
}

interface SignupStore {
  currentStep: number;
  totalSteps: number;
  signupData: SignupData;
  isSubmitting: boolean;

  // Actions
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  updateSignupData: (data: Partial<SignupData>) => void;
  resetSignup: () => void;
  isStepValid: (step: number) => boolean;
  submitSignup: () => Promise<{ success: boolean; message: string; data?: any }>;
}

const initialSignupData: SignupData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  username: '',
  dateOfBirth: '',
  gender: '',
  address: '',
  city: '',
  country: '',
  userType: 'customer',
  storeName: '',
  businessType: undefined,
  openHours: '',
  location: undefined,
  notifications: true,
  terms: false,
};

export const useSignupStore = create<SignupStore>((set, get) => ({
  currentStep: 1,
  totalSteps: 6,
  signupData: initialSignupData,
  isSubmitting: false,
  
  setCurrentStep: (step) => set({ currentStep: step }),
  
  nextStep: () => set((state) => ({ 
    currentStep: Math.min(state.currentStep + 1, state.totalSteps) 
  })),
  
  prevStep: () => set((state) => ({ 
    currentStep: Math.max(state.currentStep - 1, 1) 
  })),
  
  updateSignupData: (data) => set((state) => ({
    signupData: { ...state.signupData, ...data }
  })),
  
  resetSignup: () => set({
    currentStep: 1,
    signupData: initialSignupData,
    isSubmitting: false,
  }),
  
  isStepValid: (step) => {
    const { signupData } = get();
    
    switch (step) {
      case 1: // Basic Info
        return !!(signupData.firstName && signupData.lastName && signupData.email);
      case 2: // Contact & Security
        return !!(signupData.phone && signupData.password && signupData.confirmPassword && 
                 signupData.password === signupData.confirmPassword);
      case 3: // Profile Setup
        return !!(signupData.username && signupData.dateOfBirth && signupData.gender);
      case 4: // Address Info
        return !!(signupData.address && signupData.city && signupData.country);
      case 5: // Business Info (conditional)
        if (signupData.userType === 'supplier') {
          return !!(signupData.storeName && signupData.businessType && signupData.openHours);
        }
        return true; // Skip for customers
      case 6: // Final step
        return signupData.terms;
      default:
        return false;
    }
  },

  submitSignup: async () => {
    const { signupData } = get();
    console.log('submitSignup called with data:', signupData);

    set({ isSubmitting: true });

    try {
      // Transform data to match backend API
      const apiData = {
        firstName: signupData.firstName,
        lastName: signupData.lastName,
        email: signupData.email,
        phoneNumber: signupData.phone,
        password: signupData.password,
        username: signupData.username,
        dateOfBirth: signupData.dateOfBirth,
        gender: signupData.gender || undefined,
        address: signupData.address,
        city: signupData.city,
        country: signupData.country,
        role: signupData.userType,
        storeName: signupData.userType === 'supplier' ? signupData.storeName : undefined,
        businessType: signupData.userType === 'supplier' ? signupData.businessType as 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other' | undefined : undefined,
        openHours: signupData.userType === 'supplier' ? signupData.openHours : undefined,
        location: signupData.location,
        notifications: signupData.notifications,
      };

      console.log('Sending API data:', apiData);
      const response = await apiService.signup(apiData);
      console.log('API response:', response);

      set({ isSubmitting: false });

      if (response.success) {
        return {
          success: true,
          message: response.message || 'Account created successfully! Please check your email to verify your account.',
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.message || 'Failed to create account. Please try again.'
        };
      }
    } catch (error) {
      set({ isSubmitting: false });

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network error. Please check your connection and try again.'
      };
    }
  },
}));
