import { Card, YStack, H2, H4, <PERSON>Stack, Paragraph, Spacer, Input, Spinner, Text, View, Image, H1, Button } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView, Dimensions, ImageBackground, StatusBar } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useState, useMemo, useEffect } from 'react';
import { getCategories, Category } from '../../services/apiService';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

// Hook for responsive values
const useResponsive = () => {
    const [screenData, setScreenData] = useState(Dimensions.get('window'));

    useEffect(() => {
        const subscription = Dimensions.addEventListener('change', ({ window }) => {
            setScreenData(window);
        });

        return () => subscription?.remove();
    }, []);

    const isTablet = screenData.width >= 768;
    const isDesktop = screenData.width >= 1024;

    const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
        if (isDesktop) return desktop;
        if (isTablet) return tablet;
        return mobile;
    };

    return {
        isTablet,
        isDesktop,
        getResponsiveValue,
        screenWidth: screenData.width,
        screenHeight: screenData.height
    };
};

// Helper function to get gradient colors from bgGradient string
const getGradientColors = (bgGradient?: string): string[] => {
    if (!bgGradient) return ['#8F3DD2', '#6B46C1'];

    // Parse Tailwind gradient classes to actual colors
    const gradientMap: Record<string, string[]> = {
        'from-red-500 via-orange-500 to-yellow-500': ['#EF4444', '#F97316', '#EAB308'],
        'from-teal-500 via-cyan-500 to-blue-500': ['#14B8A6', '#06B6D4', '#3B82F6'],
        'from-blue-500 via-indigo-500 to-purple-500': ['#3B82F6', '#6366F1', '#8B5CF6'],
        'from-green-500 via-emerald-500 to-teal-500': ['#22C55E', '#10B981', '#14B8A6'],
        'restaurants': ['#FF6B6B', '#FF8E53'],
        'clothings': ['#4ECDC4', '#44A08D'],
        'pharmacies': ['#A8E6CF', '#7FCDCD'],
        'supermarkets': ['#FFD93D', '#FF6B6B'],
    };

    return gradientMap[bgGradient] || ['#8F3DD2', '#6B46C1'];
};

// Enhanced category gradients
const getCategoryGradient = (categoryKey: string): string[] => {
    const gradients: Record<string, string[]> = {
        'restaurants': ['#FF6B6B', '#FF8E53'],
        'clothings': ['#4ECDC4', '#44A08D'],
        'pharmacies': ['#A8E6CF', '#7FCDCD'],
        'supermarkets': ['#FFD93D', '#FF6B6B'],
        'electronics': ['#667eea', '#764ba2'],
        'books': ['#f093fb', '#f5576c'],
        'sports': ['#4facfe', '#00f2fe'],
        'beauty': ['#43e97b', '#38f9d7'],
        'home': ['#fa709a', '#fee140'],
        'automotive': ['#a8edea', '#fed6e3'],
    };

    return gradients[categoryKey] || ['#667eea', '#764ba2'];
};

// Helper function to get category subtitles
const getCategorySubtitle = (categoryKey: string, t: any): string => {
    const subtitles: Record<string, string> = {
        restaurants: t('categories.restaurantsDesc', { defaultValue: 'Delicious food from local restaurants' }),
        clothing: t('categories.clothingDesc', { defaultValue: 'Fashion & apparel from top brands' }),
        electronics: t('categories.electronicsDesc', { defaultValue: 'Latest tech & electronic devices' }),
        groceries: t('categories.groceriesDesc', { defaultValue: 'Fresh groceries & daily essentials' }),
        pharmacy: t('categories.pharmacyDesc', { defaultValue: 'Medicines & health products' }),
        books: t('categories.booksDesc', { defaultValue: 'Books & educational materials' }),
        beauty: t('categories.beautyDesc', { defaultValue: 'Beauty & personal care products' }),
        sports: t('categories.sportsDesc', { defaultValue: 'Sports equipment & fitness gear' }),
        home: t('categories.homeDesc', { defaultValue: 'Home & garden essentials' }),
        automotive: t('categories.automotiveDesc', { defaultValue: 'Car parts & automotive services' }),
    };

    return subtitles[categoryKey] || t('categories.defaultDesc', { defaultValue: 'Quality products & services' });
};

export const SupplierCategoriesGUI = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const insets = useSafeAreaInsets();
    const { isTablet, isDesktop, getResponsiveValue } = useResponsive();
    const [searchQuery, setSearchQuery] = useState('');
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setLoading(true);
                setError(null);
                const categoriesData = await getCategories();
                setCategories(categoriesData);
            } catch (error) {
                console.error('Error fetching categories:', error);
                setError('Failed to load categories. Please try again.');
                setCategories([]);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const handleImageError = (categoryKey: string) => {
        setImageErrors(prev => ({ ...prev, [categoryKey]: true }));
    };

    const handleRetry = () => {
        setError(null);
        setLoading(true);
        // Re-trigger the useEffect
        const fetchCategories = async () => {
            try {
                const categoriesData = await getCategories();
                setCategories(categoriesData);
            } catch (error) {
                console.error('Error fetching categories:', error);
                setError('Failed to load categories. Please try again.');
                setCategories([]);
            } finally {
                setLoading(false);
            }
        };
        fetchCategories();
    };

    const filteredCategories = useMemo(() => {
        if (!searchQuery.trim()) return categories;
        return categories.filter(category =>
            t(`categories.${category.key}`, { defaultValue: category.label })
                .toLowerCase()
                .includes(searchQuery.toLowerCase())
        );
    }, [categories, searchQuery, t]);

    if (loading) {
        return (
            <YStack f={1} ai="center" jc="center" bg="$background">
                <Spinner size="large" color="$primary" />
                <Text mt="$3" color="$gray10">
                    {t('categories.loading', { defaultValue: 'Loading categories...' })}
                </Text>
            </YStack>
        );
    }

    if (error) {
        return (
            <YStack f={1} ai="center" jc="center" bg="$background" p="$4">
                <Ionicons name="alert-circle-outline" size={64} color="#EF4444" />
                <Text mt="$3" color="$gray10" ta="center" fontSize="$5" fontWeight="600">
                    {t('categories.error', { defaultValue: 'Oops! Something went wrong' })}
                </Text>
                <Text mt="$2" color="$gray8" ta="center" fontSize="$3">
                    {error}
                </Text>
                <Button
                    mt="$4"
                    onPress={handleRetry}
                    backgroundColor="$primary"
                    color="white"
                    borderRadius="$6"
                    paddingHorizontal="$6"
                    paddingVertical="$3"
                >
                    <XStack ai="center" gap="$2">
                        <Ionicons name="refresh-outline" size={20} color="white" />
                        <Text color="white" fontWeight="600">
                            {t('categories.retry', { defaultValue: 'Try Again' })}
                        </Text>
                    </XStack>
                </Button>
            </YStack>
        );
    }

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor="#667eea" />

            {/* Professional Header with Enhanced Gradient */}
            <LinearGradient
                colors={['#667eea', '#764ba2', '#f093fb']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{
                    paddingTop: insets.top + getResponsiveValue(25, 30, 35),
                    paddingBottom: getResponsiveValue(30, 35, 40),
                    borderBottomLeftRadius: getResponsiveValue(25, 30, 35),
                    borderBottomRightRadius: getResponsiveValue(25, 30, 35),
                }}
            >
                <MotiView
                    from={{ opacity: 0, translateY: -30 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 800 }}
                    style={{
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                    }}
                >
                    <YStack gap={getResponsiveValue(16, 20, 24)}>
                        {/* Welcome Message */}
                        <YStack gap="$2" alignItems="center">
                            <Text
                                color="rgba(255,255,255,0.9)"
                                fontSize={getResponsiveValue(16, 18, 20)}
                                fontWeight="500"
                                textAlign="center"
                            >
                                {t('categories.welcome', { defaultValue: 'Welcome to BolTalab' })}
                            </Text>
                        </YStack>

                        {/* Main Title */}
                        <YStack gap="$2">
                            <H1
                                color="white"
                                fontWeight="900"
                                fontSize={getResponsiveValue(32, 36, 40)}
                                lineHeight={getResponsiveValue(1.2, 1.3, 1.4)}
                                textAlign="center"
                            >
                                {t('categories.title', { defaultValue: 'Supplier Categories' })}
                            </H1>
                            <Text
                                color="rgba(255,255,255,0.8)"
                                fontSize={getResponsiveValue(16, 18, 20)}
                                textAlign="center"
                                fontWeight="500"
                            >
                                {t('categories.subtitle', { defaultValue: 'Discover amazing suppliers in your area' })}
                            </Text>
                        </YStack>

                        {/* Location Badge */}
                        <MotiView
                            from={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ type: 'timing', duration: 600, delay: 300 }}
                            style={{ alignSelf: 'center' }}
                        >
                            <View
                                backgroundColor="rgba(255,255,255,0.2)"
                                paddingHorizontal="$4"
                                paddingVertical="$2"
                                borderRadius="$6"
                                borderWidth={1}
                                borderColor="rgba(255,255,255,0.3)"
                            >
                                <XStack alignItems="center" gap="$2">
                                    <Ionicons name="location" size={getResponsiveValue(16, 18, 20)} color="white" />
                                    <Text
                                        color="white"
                                        fontSize={getResponsiveValue(14, 16, 18)}
                                        fontWeight="600"
                                    >
                                        {t('location.nablus', { defaultValue: 'Nablus, Palestine' })}
                                    </Text>
                                </XStack>
                            </View>
                        </MotiView>

                        {/* Enhanced Search */}
                        <MotiView
                            from={{ opacity: 0, translateY: 20 }}
                            animate={{ opacity: 1, translateY: 0 }}
                            transition={{ type: 'timing', duration: 600, delay: 500 }}
                        >
                            <View position="relative">
                                <Input
                                    placeholder={t('categories.searchSuppliers', { defaultValue: 'Search categories...' })}
                                    size="$4"
                                    borderWidth={2}
                                    borderColor="rgba(255,255,255,0.3)"
                                    backgroundColor="rgba(255,255,255,0.15)"
                                    color="white"
                                    placeholderTextColor="rgba(255,255,255,0.7)"
                                    borderRadius="$6"
                                    paddingLeft="$5"
                                    paddingRight="$5"
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    fontSize={getResponsiveValue(14, 16, 18)}
                                    focusStyle={{
                                        borderColor: "rgba(255,255,255,0.6)",
                                        backgroundColor: "rgba(255,255,255,0.25)",
                                    }}
                                />
                                <View
                                    position="absolute"
                                    left="$3"
                                    top="50%"
                                    transform={[{ translateY: -10 }]}
                                    pointerEvents="none"
                                >
                                    <Ionicons name="search" size={20} color="rgba(255,255,255,0.8)" />
                                </View>
                                {searchQuery.trim() && (
                                    <Pressable
                                        onPress={() => setSearchQuery('')}
                                        style={{
                                            position: 'absolute',
                                            right: 12,
                                            top: '50%',
                                            transform: [{ translateY: -10 }],
                                            padding: 4,
                                        }}
                                    >
                                        <Ionicons name="close-circle" size={20} color="rgba(255,255,255,0.8)" />
                                    </Pressable>
                                )}
                            </View>
                        </MotiView>
                    </YStack>
                </MotiView>
            </LinearGradient>

            {/* Main Content Area */}
            <YStack flex={1} backgroundColor="$background">
                {/* Categories Section Header */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 600, delay: 600 }}
                    style={{
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                        paddingTop: getResponsiveValue(25, 30, 35),
                        paddingBottom: getResponsiveValue(15, 20, 25),
                    }}
                >
                    <YStack gap="$2">
                        <H2 fontWeight="800" color="$gray12" fontSize={getResponsiveValue(24, 28, 32)}>
                            {t('categories.exploreCategories', { defaultValue: 'Explore Categories' })}
                        </H2>
                        <Text color="$gray10" fontSize={getResponsiveValue(14, 16, 18)}>
                            {t('categories.categoriesSubtitle', { defaultValue: 'Find exactly what you need from our trusted suppliers' })}
                        </Text>
                    </YStack>
                </MotiView>

                {/* Enhanced Categories Grid */}
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                        paddingBottom: 100
                    }}
                    style={{ flex: 1 }}
                >
                    <YStack gap="$4">
                        {filteredCategories.length === 0 ? (
                            <MotiView
                                from={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ type: 'timing', duration: 500 }}
                            >
                                <YStack alignItems="center" justifyContent="center" paddingVertical="$8" gap="$3">
                                    <View
                                        backgroundColor="$gray3"
                                        padding="$4"
                                        borderRadius="$6"
                                    >
                                        <Ionicons name="search-outline" size={48} color="#9CA3AF" />
                                    </View>
                                    <Text
                                        marginTop="$3"
                                        color="$gray10"
                                        textAlign="center"
                                        fontSize="$5"
                                        fontWeight="600"
                                    >
                                        {t('categories.noResults', { defaultValue: 'No categories found' })}
                                    </Text>
                                    <Text
                                        color="$gray8"
                                        textAlign="center"
                                        fontSize="$3"
                                    >
                                        {t('categories.tryDifferentSearch', { defaultValue: 'Try a different search term' })}
                                    </Text>
                                </YStack>
                            </MotiView>
                        ) : (
                            <YStack gap="$4">
                                {filteredCategories.reduce((rows: any[], category, index) => {
                                    const rowIndex = Math.floor(index / 2);
                                    if (!rows[rowIndex]) rows[rowIndex] = [];
                                    rows[rowIndex].push(category);
                                    return rows;
                                }, []).map((row, rowIndex) => (
                                    <XStack key={rowIndex} gap="$3" justifyContent="space-between">
                                        {row.map((category, cardIndex) => {
                                            const gradientColors = getCategoryGradient(category.key);
                                            return (
                                                <MotiView
                                                    key={category.key}
                                                    from={{ opacity: 0, scale: 0.8, translateY: 40 }}
                                                    animate={{ opacity: 1, scale: 1, translateY: 0 }}
                                                    transition={{
                                                        type: 'timing',
                                                        duration: 700,
                                                        delay: 700 + (rowIndex * 200) + (cardIndex * 150)
                                                    }}
                                                    style={{ flex: 1 }}
                                                >
                                                    <Pressable
                                                        onPress={() => router.push(category.route as any)}
                                                        style={({ pressed }) => ({
                                                            transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }],
                                                            flex: 1,
                                                        })}
                                                    >
                                                        <LinearGradient
                                                            colors={gradientColors}
                                                            start={{ x: 0, y: 0 }}
                                                            end={{ x: 1, y: 1 }}
                                                            style={{
                                                                borderRadius: getResponsiveValue(20, 24, 28),
                                                                padding: getResponsiveValue(20, 24, 28),
                                                                minHeight: getResponsiveValue(180, 200, 220),
                                                                shadowColor: '#000',
                                                                shadowOffset: { width: 0, height: 8 },
                                                                shadowOpacity: 0.2,
                                                                shadowRadius: 16,
                                                                elevation: 12,
                                                                position: 'relative',
                                                                overflow: 'hidden',
                                                            }}
                                                        >
                                                            {/* Background Pattern */}
                                                            <View
                                                                position="absolute"
                                                                top={-20}
                                                                right={-20}
                                                                width={80}
                                                                height={80}
                                                                backgroundColor="rgba(255,255,255,0.1)"
                                                                borderRadius="$10"
                                                            />
                                                            <View
                                                                position="absolute"
                                                                bottom={-30}
                                                                left={-30}
                                                                width={100}
                                                                height={100}
                                                                backgroundColor="rgba(255,255,255,0.05)"
                                                                borderRadius="$10"
                                                            />

                                                            {/* Badge */}
                                                            {category.badge && (
                                                                <View
                                                                    position="absolute"
                                                                    top={getResponsiveValue(12, 16, 20)}
                                                                    right={getResponsiveValue(12, 16, 20)}
                                                                    backgroundColor="rgba(255,255,255,0.95)"
                                                                    borderRadius="$5"
                                                                    paddingHorizontal="$3"
                                                                    paddingVertical="$1"
                                                                    zIndex={10}
                                                                    shadowColor="$shadowColor"
                                                                    shadowOffset={{ width: 0, height: 2 }}
                                                                    shadowOpacity={0.1}
                                                                    shadowRadius={4}
                                                                    elevation={3}
                                                                >
                                                                    <Text
                                                                        fontSize="$2"
                                                                        fontWeight="700"
                                                                        color={gradientColors[0]}
                                                                    >
                                                                        {category.badge}
                                                                    </Text>
                                                                </View>
                                                            )}

                                                            {/* Content */}
                                                            <YStack flex={1} alignItems="center" justifyContent="center" gap="$3">
                                                                {/* Icon Container */}
                                                                <View
                                                                    backgroundColor="rgba(255,255,255,0.25)"
                                                                    padding="$4"
                                                                    borderRadius="$8"
                                                                    borderWidth={2}
                                                                    borderColor="rgba(255,255,255,0.3)"
                                                                    shadowColor="$shadowColor"
                                                                    shadowOffset={{ width: 0, height: 4 }}
                                                                    shadowOpacity={0.1}
                                                                    shadowRadius={8}
                                                                    elevation={4}
                                                                >
                                                                    {category.image && !imageErrors[category.key] ? (
                                                                        <Image
                                                                            source={{ uri: category.image }}
                                                                            width={getResponsiveValue(40, 48, 56)}
                                                                            height={getResponsiveValue(40, 48, 56)}
                                                                            borderRadius="$4"
                                                                            backgroundColor="rgba(255,255,255,0.1)"
                                                                            onError={() => handleImageError(category.key)}
                                                                        />
                                                                    ) : (
                                                                        <Ionicons
                                                                            name={category.icon as any}
                                                                            size={getResponsiveValue(40, 48, 56)}
                                                                            color="white"
                                                                        />
                                                                    )}
                                                                </View>

                                                                {/* Category Info */}
                                                                <YStack gap="$1" alignItems="center">
                                                                    <Text
                                                                        color="white"
                                                                        fontWeight="800"
                                                                        textAlign="center"
                                                                        fontSize={getResponsiveValue(16, 18, 20)}
                                                                        lineHeight={getResponsiveValue(1.3, 1.4, 1.5)}
                                                                    >
                                                                        {t(`categories.${category.key}`, { defaultValue: category.label })}
                                                                    </Text>
                                                                    <Text
                                                                        color="rgba(255,255,255,0.85)"
                                                                        textAlign="center"
                                                                        fontSize={getResponsiveValue(12, 14, 16)}
                                                                        numberOfLines={2}
                                                                        fontWeight="500"
                                                                    >
                                                                        {category.subtitle || getCategorySubtitle(category.key, t)}
                                                                    </Text>
                                                                </YStack>
                                                            </YStack>
                                                        </LinearGradient>
                                                    </Pressable>
                                                </MotiView>
                                            );
                                        })}
                                        {/* Add empty space if odd number of categories in last row */}
                                        {row.length === 1 && <View flex={1} />}
                                    </XStack>
                                ))}
                            </YStack>
                        )}
                    </YStack>
                </ScrollView>
            </YStack>
        </>
    );
};