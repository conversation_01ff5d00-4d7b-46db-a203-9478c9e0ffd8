import { Alert } from 'react-native';
import { TFunction } from 'i18next';
import { getErrorMessage } from './errorHandler';

/**
 * Shows a user-friendly error alert using React Native's Alert component
 * @param error - The error object, response, or string
 * @param t - The translation function from i18next
 */
export const showErrorAlert = (error: any, t: TFunction): void => {
  const userFriendlyError = getErrorMessage(error, t);
  
  Alert.alert(
    userFriendlyError.title,
    userFriendlyError.message,
    [{ text: t('common.ok', { defaultValue: 'OK' }) }]
  );
};
