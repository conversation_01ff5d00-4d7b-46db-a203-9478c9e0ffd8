import { <PERSON>, <PERSON><PERSON>tack, H2, H4, <PERSON>S<PERSON>ck, Paragraph, Spacer, Input, Spinner, Text, Button, Separator, H1, H3, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, View as RNView, ScrollView, Modal, Alert, Dimensions, StatusBar } from 'react-native';
import { useRouter } from 'expo-router';
import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AIChatGUI } from './AIChatGUI';
import { getServices, Service, comprehensiveSearch, SearchResult, ComprehensiveSearchResults } from '../../services/apiService';
import { useCurrentUserData } from '../useCurrentUserData';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useLanguageStore } from '../../stores/languageStore';

// Hook for responsive values
const useResponsive = () => {
    const [screenData, setScreenData] = useState(Dimensions.get('window'));

    useEffect(() => {
        const subscription = Dimensions.addEventListener('change', ({ window }) => {
            setScreenData(window);
        });

        return () => subscription?.remove();
    }, []);

    const isTablet = screenData.width >= 768;
    const isDesktop = screenData.width >= 1024;

    const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
        if (isDesktop) return desktop;
        if (isTablet) return tablet;
        return mobile;
    };

    return {
        isTablet,
        isDesktop,
        getResponsiveValue,
        screenWidth: screenData.width,
        screenHeight: screenData.height
    };
};

// Helper function to get gradient colors and subtitles for each service
const getServiceGradient = (serviceKey: string): string[] => {
    const gradients: Record<string, string[]> = {
        orderFromSupplier: ['#667eea', '#764ba2'],
        sendPackage: ['#f093fb', '#f5576c'],
        requestPickup: ['#4facfe', '#00f2fe'],
        aiChatSystem: ['#43e97b', '#38f9d7'],
        trackMyOrders: ['#fa709a', '#fee140'],
        trackMyPackages: ['#a8edea', '#fed6e3'],
        default: ['#667eea', '#764ba2']
    };

    return gradients[serviceKey] || gradients.default;
};

// Helper function to get service subtitles
const getServiceSubtitle = (serviceKey: string, t: any): string => {
    const subtitles: Record<string, string> = {
        shopOrder: t('services.orderFromSupplierDesc', { defaultValue: 'Browse & order from local suppliers' }),
        sendPackage: t('services.sendPackageDesc', { defaultValue: 'Quick & reliable package delivery' }),
        requestPickup: t('services.requestPickupDesc', { defaultValue: 'Schedule convenient pickups' }),
        customDelivery: t('services.aiChatSystemDesc', { defaultValue: 'Smart AI assistant for help' }),
        trackOrders: t('services.trackMyOrdersDesc', { defaultValue: 'Monitor your order status' }),
        trackPackages: t('services.trackMyPackagesDesc', { defaultValue: 'Track package deliveries' }),
    };

    return subtitles[serviceKey] || '';
};

export const CustomerHomeGUI = () => {
    const { t, ready } = useTranslation();
    const { isRTL } = useLanguageStore();
    const router = useRouter();
    const insets = useSafeAreaInsets();
    const { isTablet, isDesktop, getResponsiveValue } = useResponsive();

    // Don't render until translations are ready
    if (!ready) {
        return null;
    }
    const { user } = useCurrentUserData();
    const [isAIChatModalVisible, setIsAIChatModalVisible] = useState(false);
    const [services, setServices] = useState<Service[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
    const [isSearching, setIsSearching] = useState(false);
    const [showSearchResults, setShowSearchResults] = useState(false);

    // Fetch services from backend
    useEffect(() => {
        const fetchServices = async () => {
            try {
                setLoading(true);
                setError(null);
                const servicesData = await getServices();
                setServices(servicesData);
            } catch (err) {
                console.error('Error fetching services:', err);
                setError('Failed to load services. Please try again.');
                // Fallback to static services data to ensure services always display
                const fallbackServices = [
                    {
                        _id: '1',
                        key: 'shopOrder',
                        label: 'Order from Supplier',
                        icon: 'storefront-outline',
                        color: '#4A90E2',
                        route: 'home/supplier-categories',
                        isActive: true,
                        order: 1,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        _id: '2',
                        key: 'sendPackage',
                        label: 'Send a Package',
                        icon: 'cube-outline',
                        color: '#F5A623',
                        route: 'home/send-package',
                        isActive: true,
                        order: 2,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        _id: '3',
                        key: 'requestPickup',
                        label: 'Request Pickup',
                        icon: 'arrow-redo-outline',
                        color: '#50E3C2',
                        route: '/home/<USER>',
                        isActive: true,
                        order: 3,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        _id: '4',
                        key: 'customDelivery',
                        label: 'AI Chat System',
                        icon: 'chatbubble-ellipses-outline',
                        color: '#BD10E0',
                        route: '/ai-chat',
                        isActive: true,
                        order: 4,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        _id: '5',
                        key: 'trackOrders',
                        label: 'Track My Orders',
                        icon: 'locate-outline',
                        color: '#B8E986',
                        route: '/orders',
                        isActive: true,
                        order: 5,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        _id: '6',
                        key: 'trackPackages',
                        label: 'Track My Packages',
                        icon: 'locate-outline',
                        color: '#68E11f',
                        route: '/packages',
                        isActive: true,
                        order: 6,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ];
                setServices(fallbackServices);
            } finally {
                setLoading(false);
            }
        };

        fetchServices();
    }, []);

    // Helper function to detect if text contains Arabic characters
    const containsArabic = (text: string): boolean => {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
        return arabicRegex.test(text);
    };

    // Handle search functionality
    useEffect(() => {
        const performSearch = async () => {
            if (!searchQuery.trim()) {
                setSearchResults(null);
                setShowSearchResults(false);
                return;
            }

            setIsSearching(true);
            try {
                const results = await comprehensiveSearch(searchQuery);
                setSearchResults(results);
                setShowSearchResults(true);
            } catch (error) {
                console.error('Search error:', error);
                setSearchResults(null);
            } finally {
                setIsSearching(false);
            }
        };

        const timeoutId = setTimeout(performSearch, 300); // Debounce search
        return () => clearTimeout(timeoutId);
    }, [searchQuery]);

    const filteredServices = useMemo(() => {
        if (showSearchResults) return [];
        return services;
    }, [services, showSearchResults]);

    const handleServicePress = (key: string, route: string) => {
        if (key === 'customDelivery') {
            // Open AI Chat modal instead of navigating
            setIsAIChatModalVisible(true);
        } else {
            // Navigate normally for other services
            router.push(route as any);
        }
    };

    const handleSearchResultPress = (result: SearchResult) => {
        if (result.type === 'service' && result.data.key === 'customDelivery') {
            setIsAIChatModalVisible(true);
        } else {
            router.push(result.route as any);
        }
        // Clear search after navigation
        setSearchQuery('');
        setShowSearchResults(false);
    };

    const clearSearch = () => {
        setSearchQuery('');
        setSearchResults(null);
        setShowSearchResults(false);
    };

    const handleRetry = () => {
        setError(null);
        setLoading(true);
        // Re-trigger the useEffect
        getServices()
            .then(setServices)
            .catch((err) => {
                console.error('Error retrying services fetch:', err);
                setError('Failed to load services. Please try again.');
                setServices([]);
            })
            .finally(() => setLoading(false));
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor="#667eea" />

            {/* Professional Header with Gradient */}
            <LinearGradient
                colors={['#667eea', '#764ba2']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{
                    paddingTop: insets.top + getResponsiveValue(25, 30, 35),
                    paddingBottom: getResponsiveValue(25, 30, 35),
                    borderBottomLeftRadius: getResponsiveValue(20, 25, 30),
                    borderBottomRightRadius: getResponsiveValue(20, 25, 30),
                }}
            >
                <MotiView
                    from={{ opacity: 0, translateY: -20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 600 }}
                    style={{
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                    }}
                >
                    {/* Welcome Section */}
                    <YStack gap={getResponsiveValue(12, 16, 20)}>
                        {/* Welcome Message at Top */}
                        <YStack gap="$2" alignItems="center">
                            <Text
                                color="rgba(255,255,255,0.9)"
                                fontSize={getResponsiveValue(16, 18, 20)}
                                fontWeight="500"
                                textAlign="center"
                            >
                                {t('home.welcome', { defaultValue: 'Welcome to BolTalab' })}
                            </Text>
                            <H1
                                color="white"
                                fontWeight="800"
                                fontSize={getResponsiveValue(28, 32, 36)}
                                lineHeight={getResponsiveValue(1.2, 1.3, 1.4)}
                                textAlign="center"
                            >
                                {t('home.welcomeBack', {
                                    name: user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : (user?.email ? user.email.split('@')[0] : 'User'),
                                    defaultValue: 'Hello, {{name}} 👋'
                                })}
                            </H1>
                        </YStack>

                        <XStack alignItems="center" gap="$2">
                            <View
                                backgroundColor="rgba(255,255,255,0.2)"
                                padding="$2"
                                borderRadius="$3"
                            >
                                <Ionicons name="location" size={getResponsiveValue(16, 18, 20)} color="white" />
                            </View>
                            <Text
                                color="rgba(255,255,255,0.9)"
                                fontSize={getResponsiveValue(14, 16, 18)}
                                fontWeight="500"
                            >
                                {t('home.location', { defaultValue: 'Nablus, Palestine' })}
                            </Text>
                        </XStack>

                        {/* Quick Stats */}
                        <XStack gap="$4" marginTop="$3">
                            <MotiView
                                from={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ type: 'timing', duration: 500, delay: 200 }}
                            >
                                <View
                                    backgroundColor="rgba(255,255,255,0.15)"
                                    padding="$3"
                                    borderRadius="$4"
                                    borderWidth={1}
                                    borderColor="rgba(255,255,255,0.2)"
                                >
                                    <XStack alignItems="center" gap="$2">
                                        <Ionicons name="flash" size={16} color="#FFD700" />
                                        <Text color="white" fontSize="$3" fontWeight="600">
                                            {t('home.fastDelivery', { defaultValue: 'Fast As Lightning' })}
                                        </Text>
                                    </XStack>
                                </View>
                            </MotiView>

                            <MotiView
                                from={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ type: 'timing', duration: 500, delay: 400 }}
                            >
                                <View
                                    backgroundColor="rgba(255,255,255,0.15)"
                                    padding="$3"
                                    borderRadius="$4"
                                    borderWidth={1}
                                    borderColor="rgba(255,255,255,0.2)"
                                >
                                    <XStack alignItems="center" gap="$2">
                                        <Ionicons name="shield-checkmark" size={16} color="#4ADE80" />
                                        <Text color="white" fontSize="$3" fontWeight="600">
                                            {t('home.secure', { defaultValue: 'Secure' })}
                                        </Text>
                                    </XStack>
                                </View>
                            </MotiView>
                        </XStack>
                    </YStack>
                </MotiView>
            </LinearGradient>

            {/* Main Content Area */}
            <YStack flex={1} backgroundColor="$background">
                {/* Enhanced Search Section */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 300 }}
                    style={{
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                        paddingTop: getResponsiveValue(20, 25, 30),
                        paddingBottom: getResponsiveValue(15, 20, 25),
                    }}
                >
                    <XStack alignItems="center" gap="$3">
                        <View flex={1} position="relative">
                            <Input
                                placeholder={t('home.searchPlaceholder', { defaultValue: 'Search services, suppliers, or items…' })}
                                size="$4"
                                borderWidth={2}
                                borderColor="$gray6"
                                backgroundColor="white"
                                borderRadius="$5"
                                paddingLeft="$5"
                                paddingRight={searchQuery.trim() ? "$10" : "$5"}
                                value={searchQuery}
                                onChangeText={setSearchQuery}
                                fontSize={getResponsiveValue(14, 16, 18)}
                                shadowColor="$shadowColor"
                                shadowOffset={{ width: 0, height: 2 }}
                                shadowOpacity={0.1}
                                shadowRadius={8}
                                elevation={3}
                                focusStyle={{
                                    borderColor: "$purple10",
                                    shadowOpacity: 0.2,
                                }}
                            />
                            <View
                                position="absolute"
                                left="$3"
                                top="50%"
                                transform={[{ translateY: -10 }]}
                                pointerEvents="none"
                            >
                                <Ionicons name="search" size={20} color="#999" />
                            </View>
                            {searchQuery.trim() && (
                                <Pressable
                                    onPress={clearSearch}
                                    style={{
                                        position: 'absolute',
                                        right: 12,
                                        top: '50%',
                                        transform: [{ translateY: -10 }],
                                        padding: 4,
                                    }}
                                >
                                    <Ionicons name="close-circle" size={20} color="#999" />
                                </Pressable>
                            )}
                        </View>
                    </XStack>
                </MotiView>

                {/* Content Area */}
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{
                        paddingBottom: 100,
                        paddingHorizontal: getResponsiveValue(20, 30, 40),
                    }}
                >
                            {showSearchResults ? (
                                // Search Results
                                <YStack gap="$4">
                                    {isSearching ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Spinner size="large" color="$blue10" />
                                            <Text mt="$3" color="$gray10">{t('home.searching', { defaultValue: 'Searching...' })}</Text>
                                        </YStack>
                                    ) : searchResults && searchResults.total > 0 ? (
                                        <YStack gap="$4">
                                            {(() => {
                                                // Detect search language and use appropriate translations
                                                const isArabicSearch = containsArabic(searchQuery);
                                                const searchLang = isArabicSearch ? 'ar' : 'en';

                                                return (
                                                    <>
                                                        <Text fontSize="$5" fontWeight="bold">
                                                            {t('home.searchResults', { lng: searchLang, defaultValue: isArabicSearch ? 'نتائج البحث' : 'Search Results' })} ({searchResults.total})
                                                        </Text>

                                                        {/* Services Results */}
                                                        {searchResults.services.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$blue10">
                                                                    {t('home.services', { lng: searchLang, defaultValue: isArabicSearch ? 'الخدمات' : 'Services' })} ({searchResults.services.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.services.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name={result.icon as any} size={24} color={result.color} />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Categories Results */}
                                                        {searchResults.categories.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$green10">
                                                                    {t('home.categories', { lng: searchLang, defaultValue: isArabicSearch ? 'الفئات' : 'Categories' })} ({searchResults.categories.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.categories.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name={result.icon as any} size={24} color={result.color} />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Suppliers Results */}
                                                        {searchResults.suppliers.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$orange10">
                                                                    {t('home.suppliers', { lng: searchLang, defaultValue: isArabicSearch ? 'الموردين' : 'Suppliers' })} ({searchResults.suppliers.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.suppliers.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name="storefront" size={24} color="#FF6B35" />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.subtitle && (
                                                                                <Text fontSize="$3" color="$blue10">{result.subtitle}</Text>
                                                                            )}
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                </YStack>
                                            )}

                                                        {/* Products Results */}
                                                        {searchResults.products.length > 0 && (
                                                            <YStack gap="$2">
                                                                <Text fontSize="$4" fontWeight="600" color="$purple10">
                                                                    {t('home.products', { lng: searchLang, defaultValue: isArabicSearch ? 'المنتجات' : 'Products' })} ({searchResults.products.length})
                                                                </Text>
                                                    <YStack gap="$2">
                                                        {searchResults.products.map((result) => (
                                                            <Pressable
                                                                key={result.id}
                                                                onPress={() => handleSearchResultPress(result)}
                                                                style={({ pressed }) => ({
                                                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                                                })}
                                                            >
                                                                <Card p="$3" br="$3" bw="$0.5" boc="$gray5">
                                                                    <XStack ai="center" gap="$3">
                                                                        <Ionicons name="cube" size={24} color="#8B5CF6" />
                                                                        <YStack flex={1}>
                                                                            <Text fontSize="$4" fontWeight="600">{result.title}</Text>
                                                                            {result.subtitle && (
                                                                                <Text fontSize="$3" color="$orange10">{result.subtitle}</Text>
                                                                            )}
                                                                            {result.description && (
                                                                                <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                                                    {result.description}
                                                                                </Text>
                                                                            )}
                                                                        </YStack>
                                                                        <Ionicons name="chevron-forward" size={16} color="#999" />
                                                                    </XStack>
                                                                </Card>
                                                            </Pressable>
                                                        ))}
                                                    </YStack>
                                                        </YStack>
                                                        )}
                                                    </>
                                                );
                                            })()}
                                        </YStack>
                                    ) : (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Ionicons name="search-outline" size={48} color="#CCCCCC" />
                                            <Text mt="$3" color="$gray10" ta="center">
                                                {(() => {
                                                    const isArabicSearch = containsArabic(searchQuery);
                                                    const searchLang = isArabicSearch ? 'ar' : 'en';
                                                    return t('home.noSearchResults', {
                                                        lng: searchLang,
                                                        defaultValue: isArabicSearch ? 'لم يتم العثور على نتائج لبحثك' : 'No results found for your search'
                                                    });
                                                })()}
                                            </Text>
                                            <Text color="$gray8" ta="center" mt="$1">
                                                {(() => {
                                                    const isArabicSearch = containsArabic(searchQuery);
                                                    const searchLang = isArabicSearch ? 'ar' : 'en';
                                                    return t('home.tryDifferentSearch', {
                                                        lng: searchLang,
                                                        defaultValue: isArabicSearch ? 'جرب البحث عن الخدمات أو الموردين أو المنتجات' : 'Try searching for services, suppliers, or products'
                                                    });
                                                })()}
                                            </Text>
                                        </YStack>
                                    )}
                                </YStack>
                            ) : (
                                // Default Services Grid
                                <>
                                    {loading ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Spinner size="large" color="$blue10" />
                                            <Text mt="$3" color="$gray10">{t('home.loadingServices', { defaultValue: 'Loading services...' })}</Text>
                                        </YStack>
                                    ) : error ? (
                                        <YStack ai="center" jc="center" py="$8" gap="$3">
                                            <Ionicons name="alert-circle-outline" size={48} color="#FF6B6B" />
                                            <Text ta="center" color="$red10" fontSize="$4">
                                                {t('home.failedToLoad', { defaultValue: 'Failed to load services. Please try again.' })}
                                            </Text>
                                            <Pressable
                                                onPress={handleRetry}
                                                style={({ pressed }) => ({
                                                    backgroundColor: '#4A90E2',
                                                    paddingHorizontal: 20,
                                                    paddingVertical: 10,
                                                    borderRadius: 8,
                                                    transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }]
                                                })}
                                            >
                                                <Text color="white" fontWeight="600">{t('home.retry', { defaultValue: 'Retry' })}</Text>
                                            </Pressable>
                                        </YStack>
                                    ) : filteredServices.length === 0 ? (
                                        <YStack ai="center" jc="center" py="$8">
                                            <Ionicons name="cube-outline" size={48} color="#CCCCCC" />
                                            <Text mt="$3" color="$gray10">
                                                {t('home.noServicesAvailable', { defaultValue: 'No services available' })}
                                            </Text>
                                        </YStack>
                                    ) : (
                                        <>
                                            {/* Services Section Header */}
                                            <MotiView
                                                from={{ opacity: 0, translateY: 20 }}
                                                animate={{ opacity: 1, translateY: 0 }}
                                                transition={{ type: 'timing', duration: 500, delay: 400 }}
                                            >
                                                <YStack gap="$2" marginBottom="$4">
                                                    <H2 fontWeight="800" color="$gray12">
                                                        {t('home.ourServices', { defaultValue: 'Our Services' })}
                                                    </H2>
                                                    <Text color="$gray10" fontSize="$4">
                                                        {t('home.servicesSubtitle', { defaultValue: 'Choose from our wide range of delivery services' })}
                                                    </Text>
                                                </YStack>
                                            </MotiView>

                                            {/* Professional Services Grid */}
                                            <YStack gap="$4">
                                                {filteredServices.reduce((rows: any[], service, index) => {
                                                    const rowIndex = Math.floor(index / 2);
                                                    if (!rows[rowIndex]) rows[rowIndex] = [];
                                                    rows[rowIndex].push(service);
                                                    return rows;
                                                }, []).map((row, rowIndex) => (
                                                    <XStack key={rowIndex} gap="$3" justifyContent="space-between">
                                                        {row.map(({ key, label, icon, color, route }, cardIndex) => (
                                                            <MotiView
                                                                key={key}
                                                                from={{ opacity: 0, scale: 0.8, translateY: 30 }}
                                                                animate={{ opacity: 1, scale: 1, translateY: 0 }}
                                                                transition={{
                                                                    type: 'timing',
                                                                    duration: 600,
                                                                    delay: 500 + (rowIndex * 200) + (cardIndex * 100)
                                                                }}
                                                                style={{ flex: 1 }}
                                                            >
                                                                <Pressable
                                                                    onPress={() => handleServicePress(key, route)}
                                                                    style={({ pressed }) => ({
                                                                        transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }],
                                                                        flex: 1,
                                                                    })}
                                                                >
                                                                    <LinearGradient
                                                                        colors={getServiceGradient(key)}
                                                                        start={{ x: 0, y: 0 }}
                                                                        end={{ x: 1, y: 1 }}
                                                                        style={{
                                                                            borderRadius: getResponsiveValue(16, 20, 24),
                                                                            padding: getResponsiveValue(20, 24, 28),
                                                                            minHeight: getResponsiveValue(160, 180, 200),
                                                                            shadowColor: '#000',
                                                                            shadowOffset: { width: 0, height: 4 },
                                                                            shadowOpacity: 0.15,
                                                                            shadowRadius: 12,
                                                                            elevation: 8,
                                                                        }}
                                                                    >
                                                                        <YStack alignItems="center" justifyContent="center" flex={1} gap="$3">
                                                                            <View
                                                                                backgroundColor="rgba(255,255,255,0.2)"
                                                                                padding="$3"
                                                                                borderRadius="$6"
                                                                                borderWidth={1}
                                                                                borderColor="rgba(255,255,255,0.3)"
                                                                            >
                                                                                <Ionicons
                                                                                    name={icon as any}
                                                                                    size={getResponsiveValue(28, 32, 36)}
                                                                                    color="white"
                                                                                />
                                                                            </View>
                                                                            <YStack gap="$2" alignItems="center" flex={1} justifyContent="center">
                                                                                <Text
                                                                                    textAlign="center"
                                                                                    color="white"
                                                                                    fontWeight="700"
                                                                                    fontSize={16}
                                                                                    marginBottom={4}
                                                                                >
                                                                                    {label || 'Service'}
                                                                                </Text>
                                                                                <Text
                                                                                    textAlign="center"
                                                                                    color="rgba(255,255,255,0.8)"
                                                                                    fontWeight="400"
                                                                                    fontSize={12}
                                                                                    flexWrap="wrap"
                                                                                >
                                                                                    {getServiceSubtitle(key, t) || 'Service description'}
                                                                                </Text>
                                                                            </YStack>
                                                                        </YStack>
                                                                    </LinearGradient>
                                                                </Pressable>
                                                            </MotiView>
                                                        ))}
                                                        {/* Add empty space if odd number of services in last row */}
                                                        {row.length === 1 && <View flex={1} />}
                                                    </XStack>
                                                ))}
                                            </YStack>
                                        </>
                                    )}
                                </>
                            )}
                </ScrollView>
            </YStack>

            {/* AI Chat Modal */}
            <Modal
              visible={isAIChatModalVisible}
              animationType="slide"
              presentationStyle="fullScreen"
              onRequestClose={() => setIsAIChatModalVisible(false)}
            >
              <AIChatGUI onClose={() => setIsAIChatModalVisible(false)} />
            </Modal>
        </>
    );
};