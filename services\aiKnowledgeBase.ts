// BolTalab AI Knowledge Base - Comprehensive training data for the AI assistant

export interface KnowledgeCategory {
  category: string;
  topics: KnowledgeTopic[];
  priority: number; // Higher priority categories get more weight
}

export interface KnowledgeTopic {
  topic: string;
  content: string;
  keywords: string[];
  examples: string[];
  arabicKeywords?: string[]; // Arabic language support
  quickActions?: string[]; // Suggested quick actions for this topic
  relatedTopics?: string[]; // Related topics for better context
  confidence: number; // Confidence score for topic matching
}

export const boltalabKnowledgeBase: KnowledgeCategory[] = [
  {
    category: "Company Information",
    priority: 8,
    topics: [
      {
        topic: "About BolTalab",
        content: "BolTalab - Fast As Lightning ⚡ is Palestine's leading comprehensive delivery and logistics platform, proudly serving Nablus and surrounding communities. We bridge the gap between customers and local businesses including restaurants, grocery stores, pharmacies, and specialty shops. Our mission is to empower local commerce while providing exceptional, lightning-fast delivery services that enhance daily life. Founded with a vision to digitize and modernize local commerce, BolTalab supports economic growth in Palestinian communities with unmatched speed and reliability.",
        keywords: ["boltalab", "about", "company", "mission", "palestine", "nablus", "platform", "local business", "fast", "lightning"],
        arabicKeywords: ["واصل", "عن", "شركة", "فلسطين", "نابلس"],
        examples: ["What is BolTalab?", "Tell me about your company", "Where do you operate?", "Company mission"],
        quickActions: ["Browse Services", "Contact Support", "Learn More"],
        relatedTopics: ["Service Areas", "Company Values", "Local Business Support"],
        confidence: 0.95
      },
      {
        topic: "Service Areas",
        content: "Wasel proudly serves Nablus city center and extends to surrounding neighborhoods including Old City, Rafidia, Faisal, and nearby villages. We're actively expanding our coverage area based on community demand. Our delivery network covers residential areas, commercial districts, and educational institutions. Real-time coverage maps are available in the app to check service availability in your specific location.",
        keywords: ["areas", "location", "coverage", "nablus", "palestine", "delivery zones", "neighborhoods", "expansion"],
        arabicKeywords: ["مناطق", "موقع", "تغطية", "نابلس", "أحياء"],
        examples: ["Do you deliver to my area?", "What areas do you cover?", "Are you available in Nablus?", "Service coverage"],
        quickActions: ["Check Coverage", "View Map", "Request Expansion"],
        relatedTopics: ["Delivery Times", "Service Availability", "Expansion Plans"],
        confidence: 0.9
      },
      {
        topic: "Company Values",
        content: "BolTalab operates on core values of reliability, community support, innovation, and customer satisfaction. We prioritize supporting local Palestinian businesses, creating employment opportunities, and contributing to economic development. Our commitment extends beyond delivery - we're building a sustainable ecosystem that benefits customers, businesses, and delivery partners alike.",
        keywords: ["values", "community", "support", "local business", "employment", "sustainability", "ecosystem"],
        arabicKeywords: ["قيم", "مجتمع", "دعم", "أعمال محلية"],
        examples: ["What are your company values?", "How do you support local business?", "Community impact"],
        quickActions: ["Learn About Impact", "Partner With Us", "Community Programs"],
        relatedTopics: ["Local Business Support", "Employment Opportunities", "Sustainability"],
        confidence: 0.85
      }
    ]
  },
  {
    category: "Services",
    priority: 10,
    topics: [
      {
        topic: "Food Delivery",
        content: "Experience Nablus's finest cuisine delivered fresh to your door. Our curated network of restaurants offers everything from traditional Palestinian dishes to international cuisine. Features include: real-time order tracking, estimated delivery times (typically 25-45 minutes), restaurant ratings and reviews, dietary filters (vegetarian, halal, etc.), and special offers. Support local restaurants while enjoying convenient delivery with temperature-controlled bags ensuring food quality.",
        keywords: ["food", "restaurant", "delivery", "meals", "cuisine", "dining", "palestinian food", "traditional", "international"],
        arabicKeywords: ["طعام", "مطعم", "توصيل", "وجبات", "مأكولات فلسطينية"],
        examples: ["I want to order food", "Find restaurants near me", "How long does food delivery take?", "Palestinian restaurants", "Best food delivery"],
        quickActions: ["Browse Restaurants", "View Menus", "Track Order", "Reorder Favorites"],
        relatedTopics: ["Restaurant Ratings", "Delivery Times", "Food Quality", "Special Offers"],
        confidence: 0.95
      },
      {
        topic: "Grocery Delivery",
        content: "Shop from Nablus's trusted supermarkets and local stores for fresh produce, household essentials, and specialty items. Our grocery service includes: fresh fruits and vegetables from local farms, household cleaning supplies, personal care items, baby and family products, and bulk ordering options. Same-day delivery available with careful handling of perishables. Support local farmers and businesses while enjoying convenient shopping from home.",
        keywords: ["grocery", "supermarket", "shopping", "essentials", "produce", "household", "fresh", "local farms", "bulk"],
        arabicKeywords: ["بقالة", "سوبر ماركت", "تسوق", "خضار", "فواكه"],
        examples: ["I need groceries", "Order from supermarket", "Buy household items", "Fresh vegetables", "Local produce"],
        quickActions: ["Shop Groceries", "View Weekly Deals", "Bulk Orders", "Fresh Produce"],
        relatedTopics: ["Fresh Produce", "Local Suppliers", "Bulk Ordering", "Weekly Specials"],
        confidence: 0.9
      },
      {
        topic: "Pharmacy Delivery",
        content: "Access essential medications and health products from licensed pharmacies with professional pharmaceutical care. Services include: prescription medications with doctor verification, over-the-counter medicines, health and wellness products, medical equipment and supplies, emergency medication delivery (within 30 minutes), and consultation with licensed pharmacists. All medications are handled with strict safety protocols and temperature control when required.",
        keywords: ["pharmacy", "medication", "medicine", "prescription", "health", "drugs", "emergency", "consultation", "licensed"],
        arabicKeywords: ["صيدلية", "دواء", "وصفة طبية", "صحة", "طوارئ"],
        examples: ["I need medicine", "Order from pharmacy", "Get prescription delivered", "Emergency medication", "Health products"],
        quickActions: ["Find Pharmacy", "Upload Prescription", "Emergency Delivery", "Health Consultation"],
        relatedTopics: ["Emergency Services", "Prescription Verification", "Health Consultation", "Medical Supplies"],
        confidence: 0.95
      },
      {
        topic: "Package Sending",
        content: "Professional courier services for documents, packages, and important deliveries throughout Nablus and surrounding areas. Service options include: Standard delivery (same day, 4-8 hours), Express delivery (2-4 hours), Urgent delivery (within 1 hour), and Scheduled delivery (specific time slots). Features include real-time tracking, delivery confirmation with photos, insurance options for valuable items, and bulk sending discounts for businesses.",
        keywords: ["package", "send", "documents", "delivery", "shipping", "courier", "express", "urgent", "tracking", "business"],
        arabicKeywords: ["طرد", "إرسال", "وثائق", "توصيل", "عاجل"],
        examples: ["Send a package", "How to ship documents", "Package delivery options", "Express delivery", "Business shipping"],
        quickActions: ["Send Package", "Track Package", "Schedule Pickup", "Business Solutions"],
        relatedTopics: ["Delivery Options", "Package Tracking", "Business Services", "Insurance Options"],
        confidence: 0.9
      },
      {
        topic: "Pickup Requests",
        content: "Convenient pickup services for any items you need to send, whether personal or business-related. Our professional drivers will collect packages, documents, or other items from your specified location. Features include: Immediate pickup (within 1 hour), Scheduled pickup (choose your preferred time), Multiple item pickup, Business pickup solutions, and Secure handling with tracking. Perfect for busy professionals, businesses, and anyone needing reliable pickup services.",
        keywords: ["pickup", "collect", "retrieve", "schedule", "driver", "immediate", "business", "professional", "secure"],
        arabicKeywords: ["استلام", "جمع", "جدولة", "سائق", "فوري"],
        examples: ["Request pickup", "Schedule collection", "Pick up my package", "Business pickup", "Immediate pickup"],
        quickActions: ["Request Pickup", "Schedule Pickup", "Business Pickup", "Track Pickup"],
        relatedTopics: ["Delivery Services", "Business Solutions", "Scheduling Options", "Professional Services"],
        confidence: 0.85
      }
    ]
  },
  {
    category: "Order Management",
    priority: 9,
    topics: [
      {
        topic: "Order Tracking",
        content: "Advanced real-time order tracking with GPS-enabled delivery updates. Track your order through every stage: Order Confirmed → Supplier Preparing → Quality Check → Driver Assigned → On the Way → Delivered. Features include: Live GPS tracking when driver is en route, Estimated delivery time updates, Push notifications for each status change, Direct communication with driver via in-app chat, Photo confirmation upon delivery, and Delivery history with detailed timelines.",
        keywords: ["track", "tracking", "order status", "notifications", "driver contact", "gps", "real-time", "delivery updates"],
        arabicKeywords: ["تتبع", "حالة الطلب", "إشعارات", "سائق", "توصيل"],
        examples: ["Track my order", "Where is my delivery?", "Order status update", "Contact driver", "Delivery time"],
        quickActions: ["Track Order", "Contact Driver", "View History", "Get Updates"],
        relatedTopics: ["Delivery Status", "Driver Communication", "Delivery Confirmation", "Order History"],
        confidence: 0.95
      },
      {
        topic: "Order Modification",
        content: "Flexible order modification system allowing changes before supplier confirmation. Modification options include: Add or remove items (within 5 minutes of ordering), Change delivery address (before driver assignment), Update special instructions or preferences, Modify delivery time slot, and Change contact information. Advanced modifications may require supplier approval. Our system automatically checks modification feasibility and provides instant feedback.",
        keywords: ["modify", "change", "edit", "update", "address", "items", "instructions", "time slot", "flexible"],
        arabicKeywords: ["تعديل", "تغيير", "عنوان", "عناصر", "تعليمات"],
        examples: ["Change my order", "Update delivery address", "Add items to order", "Modify instructions", "Change delivery time"],
        quickActions: ["Modify Order", "Change Address", "Update Items", "Contact Support"],
        relatedTopics: ["Order Status", "Delivery Options", "Customer Support", "Order Confirmation"],
        confidence: 0.9
      },
      {
        topic: "Order Cancellation",
        content: "Comprehensive cancellation policy with transparent refund process. Cancellation options: Free cancellation within 5 minutes of ordering, Partial refund if supplier hasn't started preparation, No refund if order is being prepared or out for delivery (except in special circumstances). Instant refund processing for eligible cancellations. Emergency cancellation support available 24/7 for urgent situations. Refunds are processed back to original payment method within 3-5 business days.",
        keywords: ["cancel", "cancellation", "refund", "stop order", "emergency", "policy", "instant", "transparent"],
        arabicKeywords: ["إلغاء", "استرداد", "إيقاف الطلب", "طوارئ"],
        examples: ["Cancel my order", "I want to cancel", "Stop my delivery", "Refund policy", "Emergency cancellation"],
        quickActions: ["Cancel Order", "Check Refund", "Emergency Cancel", "Contact Support"],
        relatedTopics: ["Refund Policy", "Emergency Support", "Payment Methods", "Order Status"],
        confidence: 0.9
      },
      {
        topic: "Order History & Reordering",
        content: "Comprehensive order history with smart reordering features. Access detailed records of all past orders including: Order details with itemized receipts, Delivery photos and confirmations, Supplier ratings and reviews you've left, Spending analytics and trends, and Favorite items for quick reordering. Smart reorder suggestions based on your preferences and ordering patterns. Export order history for expense tracking or business purposes.",
        keywords: ["history", "past orders", "reorder", "receipts", "spending", "reviews", "analytics", "favorites", "export"],
        arabicKeywords: ["تاريخ", "طلبات سابقة", "إعادة طلب", "فواتير", "مفضلة"],
        examples: ["My order history", "Past orders", "Reorder previous items", "Order receipts", "Favorite orders"],
        quickActions: ["View History", "Reorder Favorites", "Download Receipts", "Order Analytics"],
        relatedTopics: ["Order Tracking", "Supplier Reviews", "Payment History", "Account Management"],
        confidence: 0.85
      }
    ]
  },
  {
    category: "Payment & Pricing",
    topics: [
      {
        topic: "Payment Methods",
        content: "We accept cash on delivery, credit/debit cards, and mobile payment methods. Secure payment processing for all transactions. Payment is collected upon delivery for cash orders.",
        keywords: ["payment", "cash", "card", "mobile payment", "secure", "delivery payment"],
        examples: ["How can I pay?", "Payment options", "Do you accept cards?"]
      },
      {
        topic: "Delivery Fees",
        content: "Delivery fees vary by distance, order size, and delivery speed. Standard delivery starts from 5 NIS. Free delivery available for orders above certain amounts from participating suppliers.",
        keywords: ["delivery fee", "cost", "price", "free delivery", "charges"],
        examples: ["How much is delivery?", "Delivery charges", "Free delivery minimum"]
      },
      {
        topic: "Service Fees",
        content: "Small service fee applies to orders to support platform operations. Transparent pricing with no hidden charges. All fees shown before order confirmation.",
        keywords: ["service fee", "platform fee", "charges", "transparent pricing"],
        examples: ["What are the fees?", "Service charges", "Hidden costs"]
      }
    ]
  },
  {
    category: "Account & Profile",
    topics: [
      {
        topic: "Account Creation",
        content: "Create account with email and phone number. Email verification required. Add delivery addresses, payment methods, and preferences. Secure account with strong password.",
        keywords: ["account", "signup", "register", "email", "verification", "profile"],
        examples: ["How to create account?", "Sign up process", "Account registration"]
      },
      {
        topic: "Profile Management",
        content: "Update personal information, delivery addresses, and payment methods in your profile. Manage notification preferences and order history. Change password and security settings.",
        keywords: ["profile", "update", "address", "information", "settings", "password"],
        examples: ["Update my profile", "Change address", "Manage account settings"]
      },
      {
        topic: "Order History",
        content: "View all past orders with details, dates, and amounts. Reorder favorite items quickly. Download receipts and track spending. Rate and review completed orders.",
        keywords: ["history", "past orders", "reorder", "receipts", "spending", "reviews"],
        examples: ["My order history", "Past orders", "Reorder previous items"]
      }
    ]
  },
  {
    category: "Support & Troubleshooting",
    topics: [
      {
        topic: "Common Issues",
        content: "Common issues include: delayed deliveries (check traffic/weather), wrong items (contact support immediately), payment problems (verify payment method), app issues (restart app/check internet).",
        keywords: ["issues", "problems", "delayed", "wrong items", "payment", "app problems"],
        examples: ["My order is late", "Wrong items delivered", "Payment failed", "App not working"]
      },
      {
        topic: "Customer Support",
        content: "24/7 customer support available through app chat, phone, or email. Response time: immediate for urgent issues, within 2 hours for general inquiries. Escalation available for complex issues.",
        keywords: ["support", "help", "contact", "chat", "phone", "email", "24/7"],
        examples: ["Contact support", "I need help", "Customer service", "Report problem"]
      },
      {
        topic: "Refunds & Complaints",
        content: "Full refund for undelivered orders, partial refund for missing items, quality issues handled case-by-case. Complaint resolution within 24-48 hours. Compensation available for service failures.",
        keywords: ["refund", "complaint", "compensation", "quality", "missing items", "resolution"],
        examples: ["I want a refund", "File complaint", "Missing items", "Quality issue"]
      }
    ]
  }
];

// Advanced AI Knowledge Helper with enhanced context understanding
export class AIKnowledgeHelper {

  // Find relevant knowledge with advanced scoring and context awareness
  static findRelevantKnowledge(query: string, userContext?: any): KnowledgeTopic[] {
    const queryLower = query.toLowerCase();
    const scoredTopics: Array<{topic: KnowledgeTopic, score: number}> = [];

    boltalabKnowledgeBase.forEach(category => {
      category.topics.forEach(topic => {
        let score = 0;

        // Keyword matching with weighted scoring
        const keywordMatches = topic.keywords.filter(keyword =>
          queryLower.includes(keyword.toLowerCase())
        );
        score += keywordMatches.length * 2;

        // Arabic keyword matching
        if (topic.arabicKeywords) {
          const arabicMatches = topic.arabicKeywords.filter(keyword =>
            queryLower.includes(keyword)
          );
          score += arabicMatches.length * 2;
        }

        // Example matching
        const exampleMatches = topic.examples.filter(example =>
          queryLower.includes(example.toLowerCase()) ||
          example.toLowerCase().includes(queryLower)
        );
        score += exampleMatches.length * 1.5;

        // Fuzzy matching for partial words
        const queryWords = queryLower.split(' ');
        topic.keywords.forEach(keyword => {
          queryWords.forEach(word => {
            if (word.length > 3 && keyword.toLowerCase().includes(word)) {
              score += 0.5;
            }
          });
        });

        // Category priority boost
        score *= (category.priority || 5) / 10;

        // Topic confidence boost
        score *= topic.confidence || 0.8;

        // Context-based scoring
        if (userContext) {
          score += this.getContextualScore(topic, userContext);
        }

        if (score > 0) {
          scoredTopics.push({ topic, score });
        }
      });
    });

    // Sort by score and return top topics
    return scoredTopics
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(item => item.topic);
  }

  // Calculate contextual relevance score
  private static getContextualScore(topic: KnowledgeTopic, userContext: any): number {
    let contextScore = 0;

    // Recent order context
    if (userContext.recentOrders && topic.keywords.includes('order')) {
      contextScore += 1;
    }

    // Location context
    if (userContext.location && topic.keywords.includes('delivery')) {
      contextScore += 0.5;
    }

    // Time-based context
    const hour = new Date().getHours();
    if (hour >= 11 && hour <= 14 && topic.keywords.includes('food')) {
      contextScore += 0.5; // Lunch time boost for food
    }

    return contextScore;
  }
  
  // Get enhanced context string for AI prompt with smart suggestions
  static getContextForQuery(query: string, userContext?: any): string {
    const relevantTopics = this.findRelevantKnowledge(query, userContext);

    if (relevantTopics.length === 0) {
      return "No specific context found. Use general BolTalab knowledge and provide helpful guidance.";
    }

    let context = "=== RELEVANT BOLTALAB INFORMATION ===\n\n";

    relevantTopics.forEach((topic, index) => {
      context += `${index + 1}. ${topic.topic}:\n${topic.content}\n`;

      if (topic.quickActions && topic.quickActions.length > 0) {
        context += `Quick Actions: ${topic.quickActions.join(', ')}\n`;
      }

      if (topic.relatedTopics && topic.relatedTopics.length > 0) {
        context += `Related Topics: ${topic.relatedTopics.join(', ')}\n`;
      }

      context += "\n";
    });

    // Add contextual suggestions
    const suggestions = this.getSmartSuggestions(query, userContext);
    if (suggestions.length > 0) {
      context += "=== SMART SUGGESTIONS ===\n";
      suggestions.forEach(suggestion => {
        context += `• ${suggestion}\n`;
      });
      context += "\n";
    }

    return context;
  }

  // Generate smart suggestions based on query and context
  static getSmartSuggestions(query: string, userContext?: any): string[] {
    const suggestions: string[] = [];
    const queryLower = query.toLowerCase();

    // Time-based suggestions
    const hour = new Date().getHours();
    if (hour >= 11 && hour <= 14 && !queryLower.includes('food')) {
      suggestions.push("It's lunch time! Would you like to browse restaurants?");
    }

    // Query-specific suggestions
    if (queryLower.includes('track') || queryLower.includes('order')) {
      suggestions.push("You can track orders in real-time with GPS updates");
      suggestions.push("Contact your driver directly when order is on the way");
    }

    if (queryLower.includes('food') || queryLower.includes('restaurant')) {
      suggestions.push("Try our featured Palestinian restaurants");
      suggestions.push("Check out today's special offers and discounts");
    }

    if (queryLower.includes('help') || queryLower.includes('support')) {
      suggestions.push("Our 24/7 support team is here to help");
      suggestions.push("Check our FAQ for quick answers");
    }

    // Context-based suggestions
    if (userContext?.recentOrders && userContext.recentOrders.length > 0) {
      suggestions.push("Reorder from your favorite restaurants");
    }

    return suggestions.slice(0, 3); // Limit to 3 suggestions
  }
  
  // Get all knowledge as training context
  static getAllKnowledgeContext(): string {
    let context = "Complete BolTalab Knowledge Base:\n\n";

    boltalabKnowledgeBase.forEach(category => {
      context += `=== ${category.category} ===\n\n`;
      category.topics.forEach(topic => {
        context += `${topic.topic}:\n${topic.content}\n\n`;
      });
    });
    
    return context;
  }
  
  // Get quick actions based on query intent
  static getQuickActionsForQuery(query: string): string[] {
    const queryLower = query.toLowerCase();
    const actions: string[] = [];
    
    if (queryLower.includes('track') || queryLower.includes('order')) {
      actions.push('Track Order', 'Order History', 'Contact Support');
    }
    
    if (queryLower.includes('food') || queryLower.includes('restaurant')) {
      actions.push('Browse Restaurants', 'View Menu', 'Order Food');
    }
    
    if (queryLower.includes('grocery') || queryLower.includes('shopping')) {
      actions.push('Browse Groceries', 'Shop Now', 'View Cart');
    }
    
    if (queryLower.includes('package') || queryLower.includes('send')) {
      actions.push('Send Package', 'Track Package', 'Schedule Pickup');
    }
    
    if (queryLower.includes('help') || queryLower.includes('support')) {
      actions.push('Contact Support', 'View FAQ', 'Report Issue');
    }
    
    return actions;
  }
}
