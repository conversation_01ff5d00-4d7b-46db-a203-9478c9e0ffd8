import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  CheckCircle, Clock, MapPin, Phone, CreditCard, Package, Home,
  Star, Truck, Bell, Gift, ChevronRight, Search, ArrowLeft,
  Sparkles, Trophy, Heart, Share2
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '../../components/common/Logo';
import { useOrdersStore } from '../../stores/ordersStore';

const OrderConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showCelebration, setShowCelebration] = useState(true);

  // Get orders store
  const { orders } = useOrdersStore();

  // Handle scroll for header animation with debouncing
  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    setIsHeaderCompact(currentScrollY > 600);
  }, []);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const debouncedHandleScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleScroll, 50);
    };

    window.addEventListener('scroll', debouncedHandleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', debouncedHandleScroll);
      clearTimeout(timeoutId);
    };
  }, [handleScroll]);

  // Hide celebration after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  // Get all orders from location state, or fetch recent orders from store, or use mock data
  const getOrdersToDisplay = () => {
    // First priority: orders passed via navigation state
    if (location.state?.allOrders && location.state.allOrders.length > 0) {
      return location.state.allOrders;
    }

    // Second priority: get the most recent orders from the store (last 10 minutes)
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
    const recentOrders = orders
      .filter(order => {
        // Check both placedAt and createdAt fields
        const orderTime = order.placedAt || order.createdAt;
        return orderTime && orderTime > tenMinutesAgo;
      })
      .sort((a, b) => {
        // Sort by most recent first
        const timeA = a.placedAt || a.createdAt;
        const timeB = b.placedAt || b.createdAt;
        return new Date(timeB).getTime() - new Date(timeA).getTime();
      })
      .slice(0, 5); // Get up to 5 most recent orders

    if (recentOrders.length > 0) {
      return recentOrders;
    }

    // Fallback: mock data
    return [
      {
        id: `W-${Date.now().toString().slice(-6)}-1`,
        items: [
          {
            product: { name: 'Chicken Shawarma' },
            qty: 2,
            finalPrice: 28,
            selectedAdditions: [
              { name: 'Extra Meat', price: 5 },
              { name: 'Extra Sauce', price: 2 }
            ],
            selectedSides: [
              { name: 'French Fries', price: 8 }
            ],
            without: ['Onions', 'Pickles']
          },
          {
            product: { name: 'Turkish Jacket' },
            qty: 1,
            finalPrice: 180,
            selectedSize: 'L',
            selectedColor: 'Navy Blue'
          },
          {
            product: { name: 'Margherita Pizza' },
            qty: 1,
            finalPrice: 52,
            selectedAdditions: [
              { name: 'Extra Mozzarella', price: 8 },
              { name: 'Pepperoni', price: 10 }
            ],
            without: ['Oregano']
          }
        ],
        supplier: { name: 'Al-Quds Restaurant' },
        total: 316,
        estimatedTime: '45-60 mins',
        address: 'Nablus, Palestine',
        phone: '+970568406041',
        paymentMethod: 'cash',
        status: 'Pending',
        placedAt: new Date().toISOString()
      }
    ];
  };

  const allOrders = getOrdersToDisplay();

  // Calculate total across all orders
  const grandTotal = allOrders.reduce((sum: number, order: any) => sum + order.total, 0);
  const totalOrders = allOrders.length;

  const handleTrackOrder = (orderId: string) => {
    navigate(`/customer/order-tracking?orderId=${orderId}`);
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewOrders = () => {
    navigate('/customer/orders');
  };

  const handleShareOrder = () => {
    if (navigator.share) {
      navigator.share({
        title: 'My Wasel Order',
        text: `I just placed ${totalOrders > 1 ? `${totalOrders} orders` : 'an order'} on Wasel! Total: ₪${grandTotal.toFixed(2)}`,
        url: window.location.href,
      });
    }
  };

  const handleRateExperience = () => {
    // Navigate to rating page or show modal
    console.log('Rate experience');
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-green-500/30 to-emerald-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Success celebration particles */}
          <AnimatePresence>
            {showCelebration && (
              <>
                {[...Array(30)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                    }}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                      y: [-50, -200],
                      rotate: [0, 360]
                    }}
                    transition={{
                      duration: 2 + Math.random() * 2,
                      delay: Math.random() * 1,
                    }}
                  >
                    <Sparkles className="w-4 h-4 text-yellow-400" />
                  </motion.div>
                ))}
              </>
            )}
          </AnimatePresence>

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Sticky Header with Scroll Animation */}
        <motion.div
          className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
          animate={{
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3 }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
            >
              {/* Compact Header Content */}
              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  height: isHeaderCompact ? "auto" : 0,
                }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => navigate(-1)}
                      className="p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-colors"
                    >
                      <ArrowLeft className="w-5 h-5" />
                    </motion.button>
                    <div className="flex items-center gap-3">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                      >
                        <Logo size="sm" />
                      </motion.div>
                      <div>
                        <h1 className="text-lg font-bold bg-gradient-to-r from-white via-green-100 to-emerald-100 bg-clip-text text-transparent">
                          Order Confirmed
                        </h1>
                        <p className="text-white/60 text-xs">₪{grandTotal.toFixed(2)} • {totalOrders} {totalOrders > 1 ? 'Orders' : 'Order'}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleShareOrder}
                      className="p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-colors"
                    >
                      <Share2 className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleRateExperience}
                      className="p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-colors"
                    >
                      <Heart className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <div className="relative pt-32 pb-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              {/* Success Icon with Premium Animation */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  duration: 0.8,
                  delay: 0.2,
                  type: "spring",
                  stiffness: 200,
                  damping: 20
                }}
                className="flex justify-center mb-8"
              >
                <div className="relative">
                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      boxShadow: [
                        "0 0 0 0 rgba(34, 197, 94, 0.4)",
                        "0 0 0 20px rgba(34, 197, 94, 0)",
                        "0 0 0 0 rgba(34, 197, 94, 0)"
                      ]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="bg-gradient-to-br from-green-400 to-emerald-600 rounded-full p-8 shadow-2xl"
                  >
                    <CheckCircle className="w-20 h-20 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="absolute -inset-4 border-2 border-dashed border-green-400/30 rounded-full"
                  />
                </div>
              </motion.div>

              {/* Success Message */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-100 bg-clip-text text-transparent mb-6">
                  {totalOrders > 1 ? 'Orders Placed!' : 'Order Placed!'}
                </h1>
                <p className="text-xl md:text-2xl text-white/80 mb-4 max-w-3xl mx-auto">
                  {totalOrders > 1
                    ? `🎉 Amazing! We've received ${totalOrders} orders from different suppliers.`
                    : '🎉 Thank you for your order. We\'re preparing it with love!'
                  }
                </p>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="inline-flex items-center gap-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-full px-8 py-4"
                >
                  <Trophy className="w-6 h-6 text-yellow-400" />
                  <span className="text-white font-semibold text-lg">
                    Total: ₪{grandTotal.toFixed(2)}
                  </span>
                  <Sparkles className="w-6 h-6 text-yellow-400" />
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Content Section */}
        <div className="relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">

            {/* Premium Order Details Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-12"
            >
              <div className="text-center mb-8">
                <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-4">
                  {totalOrders > 1 ? `Your ${totalOrders} Premium Orders` : 'Your Premium Order'}
                </h2>
                <p className="text-white/70 text-lg">
                  Each order is being prepared with premium care and attention
                </p>
              </div>

              <div className="grid gap-8">
                {allOrders.map((order: any, orderIndex: number) => (
                  <motion.div
                    key={order.id}
                    initial={false}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    whileHover={{
                      y: -5,
                      transition: { duration: 0.2 }
                    }}
                    className="relative group opacity-100"
                  >
                    {/* Premium Card Background */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl"></div>

                    {/* Card Content */}
                    <div className="relative p-8 md:p-10">
                      {/* Order Header */}
                      <div className="flex items-center justify-between mb-8">
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-lg">#{orderIndex + 1}</span>
                            </div>
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="absolute -inset-1 bg-green-400/30 rounded-full blur-sm"
                            />
                          </div>
                          <div>
                            <h3 className="text-2xl font-bold text-white mb-1">
                              Premium Order #{orderIndex + 1}
                            </h3>
                            <p className="text-white/70">Order ID: {order.id}</p>
                          </div>
                        </div>
                        <motion.div
                          animate={{
                            backgroundColor: ["rgba(34, 197, 94, 0.2)", "rgba(34, 197, 94, 0.4)", "rgba(34, 197, 94, 0.2)"]
                          }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="px-4 py-2 bg-green-500/20 border border-green-400/30 rounded-full"
                        >
                          <span className="text-green-300 font-semibold flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                            {order.status}
                          </span>
                        </motion.div>
                      </div>

                      {/* Premium Info Grid */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Left Column */}
                        <div className="space-y-6">
                          <motion.div
                            whileHover={{ x: 5 }}
                            className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
                          >
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center">
                              <Clock className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="font-semibold text-white mb-1">Estimated Time</p>
                              <p className="text-white/70 text-lg">{order.estimatedTime}</p>
                            </div>
                          </motion.div>

                          <motion.div
                            whileHover={{ x: 5 }}
                            className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
                          >
                            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                              <MapPin className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="font-semibold text-white mb-1">Delivery Address</p>
                              <p className="text-white/70 text-lg">{order.address}</p>
                            </div>
                          </motion.div>
                        </div>

                        {/* Right Column */}
                        <div className="space-y-6">
                          <motion.div
                            whileHover={{ x: 5 }}
                            className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
                          >
                            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
                              <Phone className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="font-semibold text-white mb-1">Contact Number</p>
                              <p className="text-white/70 text-lg">{order.phone}</p>
                            </div>
                          </motion.div>

                          <motion.div
                            whileHover={{ x: 5 }}
                            className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl border border-white/10"
                          >
                            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                              <CreditCard className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="font-semibold text-white mb-1">Payment Method</p>
                              <p className="text-white/70 text-lg capitalize">{order.paymentMethod}</p>
                            </div>
                          </motion.div>
                        </div>
                      </div>

                      {/* Total Amount Highlight */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-2xl p-6 mb-8"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center">
                              <span className="text-white text-2xl font-bold">₪</span>
                            </div>
                            <div>
                              <p className="text-white/70 text-sm uppercase tracking-wide">Total Amount</p>
                              <p className="text-white font-bold text-3xl">₪{order.total.toFixed(2)}</p>
                            </div>
                          </div>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                          >
                            <Sparkles className="w-8 h-8 text-yellow-400" />
                          </motion.div>
                        </div>
                      </motion.div>

                      {/* Premium Order Items */}
                      <div className="bg-white/5 rounded-2xl border border-white/10 p-6 mb-8">
                        <div className="flex items-center gap-3 mb-6">
                          <Package className="w-6 h-6 text-white" />
                          <h4 className="font-bold text-white text-xl">Order Items</h4>
                        </div>

                        <div className="mb-4">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                              <span className="text-white text-xs font-bold">S</span>
                            </div>
                            <span className="font-semibold text-white text-lg">From: {order.supplier.name}</span>
                          </div>
                        </div>

                        <div className="space-y-4">
                          {order.items.map((item: any, index: number) => {
                            // Helper function to format customizations
                            const formatCustomizations = () => {
                              const customizations = [];

                              // Restaurant options
                              if (item.selectedAdditions?.length > 0) {
                                customizations.push(`+${item.selectedAdditions.map((a: any) => a.name).join(', ')}`);
                              }
                              if (item.selectedSides?.length > 0) {
                                customizations.push(`Sides: ${item.selectedSides.map((s: any) => s.name).join(', ')}`);
                              }
                              if (item.without?.length > 0) {
                                customizations.push(`Without: ${item.without.join(', ')}`);
                              }

                              // Clothing options
                              if (item.selectedSize) {
                                customizations.push(`Size: ${item.selectedSize}`);
                              }
                              if (item.selectedColor) {
                                customizations.push(`Color: ${item.selectedColor}`);
                              }

                              return customizations;
                            };

                            const customizations = formatCustomizations();

                            return (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="bg-white/5 rounded-xl border border-white/10 p-4"
                              >
                                {/* Main Item Row */}
                                <div className="flex justify-between items-start mb-3">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                                      <span className="text-white font-bold">{item.qty}</span>
                                    </div>
                                    <div>
                                      <span className="text-white font-medium text-lg block">
                                        {item.product.name}
                                      </span>
                                      {customizations.length > 0 && (
                                        <span className="text-white/70 text-sm">
                                          {customizations.join(' • ')}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                  <span className="font-bold text-white text-lg">
                                    ₪{(item.finalPrice * item.qty).toFixed(2)}
                                  </span>
                                </div>

                                {/* Detailed Options Display */}
                                {customizations.length > 0 && (
                                  <div className="mt-3 pt-3 border-t border-white/10">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                      {/* Restaurant Additions */}
                                      {item.selectedAdditions?.length > 0 && (
                                        <div className="bg-white/5 rounded-lg p-3">
                                          <div className="flex items-center gap-2 mb-2">
                                            <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                              <span className="text-white text-xs">+</span>
                                            </div>
                                            <span className="text-green-300 font-semibold">Additions</span>
                                          </div>
                                          <div className="space-y-1">
                                            {item.selectedAdditions.map((addition: any, idx: number) => (
                                              <div key={idx} className="flex justify-between text-white/80">
                                                <span>{addition.name}</span>
                                                <span>+₪{addition.price}</span>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* Restaurant Sides */}
                                      {item.selectedSides?.length > 0 && (
                                        <div className="bg-white/5 rounded-lg p-3">
                                          <div className="flex items-center gap-2 mb-2">
                                            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                              <span className="text-white text-xs">S</span>
                                            </div>
                                            <span className="text-blue-300 font-semibold">Sides</span>
                                          </div>
                                          <div className="space-y-1">
                                            {item.selectedSides.map((side: any, idx: number) => (
                                              <div key={idx} className="flex justify-between text-white/80">
                                                <span>{side.name}</span>
                                                <span>+₪{side.price}</span>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* Without Options */}
                                      {item.without?.length > 0 && (
                                        <div className="bg-white/5 rounded-lg p-3">
                                          <div className="flex items-center gap-2 mb-2">
                                            <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                              <span className="text-white text-xs">-</span>
                                            </div>
                                            <span className="text-red-300 font-semibold">Without</span>
                                          </div>
                                          <div className="flex flex-wrap gap-1">
                                            {item.without.map((withoutItem: string, idx: number) => (
                                              <span key={idx} className="bg-red-500/20 text-red-200 px-2 py-1 rounded text-xs">
                                                {withoutItem}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* Clothing Options */}
                                      {(item.selectedSize || item.selectedColor) && (
                                        <div className="bg-white/5 rounded-lg p-3">
                                          <div className="flex items-center gap-2 mb-2">
                                            <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                                              <span className="text-white text-xs">C</span>
                                            </div>
                                            <span className="text-purple-300 font-semibold">Options</span>
                                          </div>
                                          <div className="space-y-1">
                                            {item.selectedSize && (
                                              <div className="flex justify-between text-white/80">
                                                <span>Size</span>
                                                <span className="bg-purple-500/20 text-purple-200 px-2 py-1 rounded text-xs">
                                                  {item.selectedSize}
                                                </span>
                                              </div>
                                            )}
                                            {item.selectedColor && (
                                              <div className="flex justify-between text-white/80">
                                                <span>Color</span>
                                                <span className="bg-purple-500/20 text-purple-200 px-2 py-1 rounded text-xs">
                                                  {item.selectedColor}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </motion.div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Premium Action Buttons */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <motion.button
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleTrackOrder(order.id)}
                          className="group relative overflow-hidden bg-gradient-to-r from-green-500 to-emerald-600 text-white py-4 px-6 rounded-2xl font-bold text-lg shadow-2xl border border-green-400/30"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <div className="relative flex items-center justify-center gap-3">
                            <Truck className="w-6 h-6" />
                            Track This Order
                            <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                          </div>
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={handleShareOrder}
                          className="group relative overflow-hidden bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-4 px-6 rounded-2xl font-bold text-lg shadow-2xl border border-blue-400/30"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <div className="relative flex items-center justify-center gap-3">
                            <Share2 className="w-6 h-6" />
                            Share Order
                            <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                          </div>
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Premium Main Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
            >
              <motion.button
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleViewOrders}
                className="group relative overflow-hidden bg-gradient-to-r from-purple-600 to-indigo-700 text-white py-6 px-8 rounded-3xl font-bold text-lg shadow-2xl border border-purple-400/30"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative flex flex-col items-center gap-3">
                  <Package className="w-8 h-8" />
                  <span>View All Orders</span>
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleGoHome}
                className="group relative overflow-hidden bg-gradient-to-r from-slate-600 to-gray-700 text-white py-6 px-8 rounded-3xl font-bold text-lg shadow-2xl border border-slate-400/30"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-slate-500 to-gray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative flex flex-col items-center gap-3">
                  <Home className="w-8 h-8" />
                  <span>Back to Home</span>
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </div>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleRateExperience}
                className="group relative overflow-hidden bg-gradient-to-r from-pink-600 to-rose-700 text-white py-6 px-8 rounded-3xl font-bold text-lg shadow-2xl border border-pink-400/30"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-rose-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative flex flex-col items-center gap-3">
                  <Star className="w-8 h-8" />
                  <span>Rate Experience</span>
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </div>
              </motion.button>
            </motion.div>

            {/* Premium What's Next Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="relative"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl"></div>
              <div className="relative p-8 md:p-10">
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Bell className="w-8 h-8 text-blue-400" />
                    <h3 className="text-2xl font-bold text-white">What happens next?</h3>
                    <Gift className="w-8 h-8 text-pink-400" />
                  </div>
                  <p className="text-white/70 text-lg">Your premium delivery experience is just beginning!</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {totalOrders > 1 ? (
                    <>
                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Package className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Multiple Suppliers</p>
                        <p className="text-white/70 text-sm">Orders sent to {totalOrders} different suppliers</p>
                      </motion.div>

                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Clock className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Independent Preparation</p>
                        <p className="text-white/70 text-sm">Each supplier prepares items separately</p>
                      </motion.div>

                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Truck className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Individual Tracking</p>
                        <p className="text-white/70 text-sm">Track each order separately</p>
                      </motion.div>
                    </>
                  ) : (
                    <>
                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Package className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Order Sent</p>
                        <p className="text-white/70 text-sm">Sent to {allOrders[0].supplier.name}</p>
                      </motion.div>

                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Bell className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Live Updates</p>
                        <p className="text-white/70 text-sm">SMS & push notifications</p>
                      </motion.div>

                      <motion.div
                        whileHover={{ y: -5 }}
                        className="bg-white/5 rounded-2xl border border-white/10 p-6 text-center"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <Truck className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-white font-semibold mb-2">Real-time Tracking</p>
                        <p className="text-white/70 text-sm">Follow your driver live</p>
                      </motion.div>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderConfirmationPage;