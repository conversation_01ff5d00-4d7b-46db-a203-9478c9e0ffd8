import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Notification {
  id: string;
  type: 'order' | 'payment' | 'system' | 'review' | 'promotion';
  title: string;
  message: string;
  timestamp: Date | string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  orderId?: string;
}

interface NotificationsStore {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: string) => void;
  clearAll: () => void;
  getNotificationsByType: (type: Notification['type']) => Notification[];
}

// Generate sample notifications
const generateSampleNotifications = (): Notification[] => [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'You have received a new order #12345 worth ₪45.50',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    read: false,
    priority: 'high',
    orderId: '12345'
  },
  {
    id: '2',
    type: 'payment',
    title: 'Payment Received',
    message: 'Payment of ₪125.00 has been processed for order #12344',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    read: false,
    priority: 'medium',
    orderId: '12344'
  },
  {
    id: '3',
    type: 'review',
    title: 'New Customer Review',
    message: 'You received a 5-star review: "Amazing food and fast delivery!"',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    read: true,
    priority: 'low'
  },
  {
    id: '4',
    type: 'system',
    title: 'System Maintenance',
    message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    read: true,
    priority: 'medium'
  },
  {
    id: '5',
    type: 'promotion',
    title: 'Promotion Opportunity',
    message: 'Boost your sales with our weekend promotion campaign!',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    read: false,
    priority: 'low'
  },
  {
    id: '6',
    type: 'order',
    title: 'Order Cancelled',
    message: 'Order #12343 has been cancelled by the customer',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    read: true,
    priority: 'medium',
    orderId: '12343'
  },
  {
    id: '7',
    type: 'payment',
    title: 'Weekly Payout Processed',
    message: 'Your weekly earnings of ₪850.00 have been transferred to your account',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    read: false,
    priority: 'high'
  },
  {
    id: '8',
    type: 'review',
    title: 'Customer Feedback',
    message: 'You received a 4-star review with feedback about delivery time',
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    read: true,
    priority: 'medium'
  }
];

export const useNotificationsStore = create<NotificationsStore>()(
  persist(
    (set, get) => {
      const initialNotifications = generateSampleNotifications();
      const unreadCount = initialNotifications.filter(n => !n.read).length;

      return {
        notifications: initialNotifications,
        unreadCount,

        addNotification: (notification) => {
          const newNotification: Notification = {
            ...notification,
            id: Date.now().toString(),
            timestamp: new Date(),
          };

          set((state) => ({
            notifications: [newNotification, ...state.notifications],
            unreadCount: state.unreadCount + 1,
          }));
        },

        markAsRead: (id) => {
          set((state) => {
            const notifications = state.notifications.map(n =>
              n.id === id ? { ...n, read: true } : n
            );
            const unreadCount = notifications.filter(n => !n.read).length;
            return { notifications, unreadCount };
          });
        },

        markAllAsRead: () => {
          set((state) => ({
            notifications: state.notifications.map(n => ({ ...n, read: true })),
            unreadCount: 0,
          }));
        },

        deleteNotification: (id) => {
          set((state) => {
            const notifications = state.notifications.filter(n => n.id !== id);
            const unreadCount = notifications.filter(n => !n.read).length;
            return { notifications, unreadCount };
          });
        },

        clearAll: () => {
          set({ notifications: [], unreadCount: 0 });
        },

        getNotificationsByType: (type) => {
          return get().notifications.filter(n => n.type === type);
        },
      };
    },
    {
      name: 'wasel-notifications-storage', // unique name for localStorage
      version: 1, // Add version to force refresh of old data
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          try {
            const data = JSON.parse(str);
            // Convert timestamp strings back to Date objects
            if (data.state?.notifications) {
              data.state.notifications = data.state.notifications.map((notification: any) => ({
                ...notification,
                timestamp: new Date(notification.timestamp)
              }));
            }
            return data;
          } catch (error) {
            // If parsing fails, return null to trigger fresh data
            console.warn('Failed to parse notifications from localStorage:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          localStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: (name) => {
          localStorage.removeItem(name);
        },
      },
    }
  )
);
