import React, { useState, useRef, useEffect } from 'react';
import {
  YStack,
  XStack,
  Text,
  Input,
  Button,
  ScrollView,
  Card,
  H3,
  Paragraph,
  Separator,
  Avatar,
  AvatarImage,
  AvatarFallback,
  Spinner
} from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { Pressable, KeyboardAvoidingView, Platform, Dimensions, Alert } from 'react-native';
import { aiService } from '../../services/aiService';
import { AIKnowledgeHelper } from '../../services/aiKnowledgeBase';
import { userContextService } from '../../services/userContextService';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  type?: 'text' | 'quick_action' | 'suggestion';
  status?: 'sending' | 'sent' | 'delivered' | 'error';
  error?: string;
}

interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: string;
  color: string;
}

interface AIChatGUIProps {
  onClose?: () => void;
}

// quickActions will be created inside the component to access translation function

// getContextualSuggestions will be created inside the component to access translation function

export const AIChatGUI = ({ onClose }: AIChatGUIProps = {}) => {
  const { t } = useTranslation();
  const router = useRouter();

  // Helper function to handle navigation with modal close
  const navigateAndClose = (route: string) => {
    if (onClose) {
      onClose();
    }
    router.push(route as any);
  };

  // Initialize with empty array to avoid undefined issues
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [conversationId, setConversationId] = useState<string>('');
  const [isOnline, setIsOnline] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  const quickActions: QuickAction[] = [
    { id: '1', label: t('chat.suggestions.trackOrder', { defaultValue: 'Track Order' }), icon: 'location-outline', action: 'track_order', color: '#4A90E2' },
    { id: '2', label: t('chat.suggestions.foodDelivery', { defaultValue: 'Food Delivery' }), icon: 'restaurant-outline', action: 'food_delivery', color: '#FF6B6B' },
    { id: '3', label: t('chat.suggestions.groceries', { defaultValue: 'Groceries' }), icon: 'basket-outline', action: 'groceries', color: '#50E3C2' },
    { id: '4', label: t('chat.suggestions.pharmacy', { defaultValue: 'Pharmacy' }), icon: 'medical-outline', action: 'pharmacy', color: '#9B59B6' },
    { id: '5', label: t('chat.suggestions.sendPackage', { defaultValue: 'Send Package' }), icon: 'cube-outline', action: 'send_package', color: '#F39C12' },
    { id: '6', label: t('chat.suggestions.requestPickup', { defaultValue: 'Request Pickup' }), icon: 'car-outline', action: 'request_pickup', color: '#E74C3C' },
    { id: '7', label: t('chat.suggestions.orderHistory', { defaultValue: 'Order History' }), icon: 'receipt-outline', action: 'order_history', color: '#3498DB' },
    { id: '8', label: t('chat.suggestions.customerSupport', { defaultValue: 'Customer Support' }), icon: 'headset-outline', action: 'customer_support', color: '#2ECC71' },
  ];

  const getContextualSuggestions = (): string[] => {
    const smartSuggestionKeys = userContextService.getSmartSuggestionKeys();
    const smartSuggestions = smartSuggestionKeys.map(key =>
      t(key, { defaultValue: key.split('.').pop() || key })
    );
    const hour = new Date().getHours();

    // Time-based suggestions
    let timeSuggestions: string[] = [];
    if (hour >= 11 && hour <= 14) {
      timeSuggestions = [
        t('chat.smartSuggestions.lunchTime', { defaultValue: "What's for lunch today? Show me restaurants" }),
        t('chat.smartSuggestions.lunchSpecials', { defaultValue: "Any lunch specials available?" }),
        t('chat.smartSuggestions.quickFood', { defaultValue: "Quick food delivery options near me" })
      ];
    } else if (hour >= 18 && hour <= 21) {
      timeSuggestions = [
        t('chat.smartSuggestions.dinnerRecommendations', { defaultValue: "Dinner recommendations for tonight" }),
        t('chat.smartSuggestions.dinnerRestaurants', { defaultValue: "What restaurants are open for dinner?" }),
        t('chat.smartSuggestions.familyMeals', { defaultValue: "Family meal deals available" })
      ];
    }

    const defaultSuggestions = [
      t('chat.smartSuggestions.trackOrder', { defaultValue: "How can I track my current order?" }),
      t('chat.smartSuggestions.bestRestaurants', { defaultValue: "What are the best restaurants near me?" }),
      t('chat.smartSuggestions.sendPackage', { defaultValue: "How do I send a package quickly?" }),
      t('chat.smartSuggestions.deliveryHours', { defaultValue: "What are your delivery hours and areas?" }),
      t('chat.smartSuggestions.modifyOrder', { defaultValue: "How can I modify or cancel my order?" }),
      t('chat.smartSuggestions.paymentMethods', { defaultValue: "What payment methods do you accept?" }),
      t('chat.smartSuggestions.specialOffers', { defaultValue: "Any special offers or discounts today?" }),
      t('chat.smartSuggestions.customerSupport', { defaultValue: "How do I contact customer support?" }),
      t('chat.smartSuggestions.schedulePickup', { defaultValue: "Can I schedule a pickup for tomorrow?" }),
      t('chat.smartSuggestions.fastestDelivery', { defaultValue: "What's the fastest delivery option?" })
    ];

    // Combine all suggestions with priority: smart > time-based > default
    const combined = [...smartSuggestions, ...timeSuggestions, ...defaultSuggestions];
    return combined.slice(0, 8); // Return top 8 for variety
  };

  // Initialize welcome message after component mounts
  useEffect(() => {
    const welcomeMessage: Message = {
      id: '1',
      text: t('chat.welcomeMessage', { defaultValue: "مرحباً! Welcome to BolTalab AI Assistant! 👋✨\n\nI'm your intelligent companion for all BolTalab services - Fast As Lightning! ⚡ Here's how I can help:\n\n🍽️ **Food Delivery** - Browse restaurants & order delicious meals\n🛒 **Grocery Shopping** - Fresh produce & household essentials  \n💊 **Pharmacy Services** - Medications & health products\n📦 **Package Delivery** - Send items anywhere in Nablus\n🚚 **Pickup Requests** - We'll collect from your location\n📱 **Order Management** - Track, modify, or get support\n\n**Ready to get started?** Ask me anything or use the quick actions below! 🌟" }),
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  }, [t]);

  const screenHeight = Dimensions.get('window').height;

  useEffect(() => {
    // Initialize conversation and user context when component mounts
    const initConversation = async () => {
      const newConversationId = aiService.createConversation();
      setConversationId(newConversationId);

      // Initialize user context
      await userContextService.initializeUserContext();
    };

    initConversation();

    // Check online status (simplified for demo)
    const checkConnection = () => {
      // In a real app, you'd use NetInfo or similar
      setIsOnline(navigator.onLine !== false);
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  }, [messages]);

  const handleSendMessage = async (text: string) => {
    if (!text.trim() || !conversationId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
      status: 'sending'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setShowQuickActions(false);
    setIsTyping(true);

    // Update message status to sent
    setTimeout(() => {
      setMessages(prev => prev.map(msg =>
        msg.id === userMessage.id ? { ...msg, status: 'sent' } : msg
      ));
    }, 500);

    try {
      // Get relevant context for the query
      const knowledgeContext = AIKnowledgeHelper.getContextForQuery(text.trim());
      const userContext = userContextService.getContextSummary();

      // Send message to AI service with comprehensive context
      const response = await aiService.sendMessage(conversationId, text.trim(), {
        knowledgeContext,
        userContext,
        timestamp: new Date().toISOString(),
        platform: 'mobile_app',
        smartSuggestions: userContextService.getSmartSuggestions()
      });

      if (response.success && response.message) {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: response.message,
          isUser: false,
          timestamp: new Date(),
          status: 'delivered'
        };
        setMessages(prev => [...prev, aiResponse]);

        // Update user message status to delivered
        setMessages(prev => prev.map(msg =>
          msg.id === userMessage.id ? { ...msg, status: 'delivered' } : msg
        ));

        setRetryCount(0); // Reset retry count on success
      } else {
        // Handle AI service failure
        await handleMessageError(userMessage, response.error || 'AI service unavailable');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      await handleMessageError(userMessage, 'Network error occurred');
    } finally {
      setIsTyping(false);
    }
  };

  const handleMessageError = async (userMessage: Message, errorMessage: string) => {
    // Update user message status to error
    setMessages(prev => prev.map(msg =>
      msg.id === userMessage.id ? { ...msg, status: 'error', error: errorMessage } : msg
    ));

    // Show fallback response after a delay
    setTimeout(() => {
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: isOnline ?
          aiService.getFallbackResponse(userMessage.text) :
          "I'm currently offline. Your message will be processed when connection is restored. 📱",
        isUser: false,
        timestamp: new Date(),
        status: 'delivered'
      };
      setMessages(prev => [...prev, fallbackResponse]);
    }, 1000);
  };

  const handleQuickAction = (action: string, label: string) => {
    // Handle navigation-based actions
    switch (action) {
      case 'track_order':
        navigateAndClose('/(customer-pages)/orders');
        break;
      case 'food_delivery':
        navigateAndClose('/(customer-pages)/home/<USER>');
        break;
      case 'groceries':
        navigateAndClose('/(customer-pages)/home/<USER>');
        break;
      case 'pharmacy':
        navigateAndClose('/(customer-pages)/home/<USER>');
        break;
      case 'send_package':
        navigateAndClose('/(customer-pages)/home/<USER>');
        break;
      case 'request_pickup':
        navigateAndClose('/(customer-pages)/home/<USER>');
        break;
      case 'order_history':
        navigateAndClose('/(customer-pages)/orders');
        break;
      case 'customer_support':
        // Send a support message to AI
        handleSendMessage("I need help with customer support");
        break;
      default:
        // For other actions, send as chat message with context
        const actionText = `I want to ${label.toLowerCase()}. Can you help me with this?`;
        handleSendMessage(actionText);
        break;
    }
  };

  const handleSuggestedQuestion = (question: string) => {
    handleSendMessage(question);
  };

  const handleRetryMessage = async (message: Message) => {
    if (message.isUser && message.status === 'error') {
      // Remove the error message and retry
      setMessages(prev => prev.filter(msg => msg.id !== message.id));
      await handleSendMessage(message.text);
    }
  };



  const MessageBubble = ({ message }: { message: Message }) => (
    <XStack
      justifyContent={message.isUser ? 'flex-end' : 'flex-start'}
      marginBottom="$3"
      paddingHorizontal="$4"
    >
      {!message.isUser && (
        <Avatar circular size="$3" marginRight="$2" marginTop="$1" borderWidth={2} borderColor="$purple8">
          <AvatarFallback backgroundColor="$purple10">
            <Ionicons name="sparkles" size={16} color="white" />
          </AvatarFallback>
        </Avatar>
      )}

      <YStack maxWidth="80%">
        <Card
          backgroundColor={message.isUser ? '$purple10' : 'white'}
          padding="$4"
          borderRadius={message.isUser ? "$5" : "$5"}
          borderTopRightRadius={message.isUser ? "$2" : "$5"}
          borderTopLeftRadius={message.isUser ? "$5" : "$2"}
          borderWidth={message.isUser ? 0 : 1}
          borderColor={message.isUser ? "transparent" : "$gray5"}
        >
          <Text
            color={message.isUser ? 'white' : '$gray12'}
            fontSize="$4"
            lineHeight="$5"
            fontWeight="400"
          >
            {message.text}
          </Text>

          <XStack justifyContent="space-between" alignItems="center" marginTop="$2">
            <Text
              color={message.isUser ? '$purple3' : '$gray8'}
              fontSize="$2"
              fontWeight="300"
            >
              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>

            {message.isUser && (
              <XStack alignItems="center" gap="$1">
                {message.status === 'sending' && <Spinner size="small" color="$purple3" />}
                {message.status === 'sent' && <Ionicons name="checkmark" size={12} color="#E3F2FD" />}
                {message.status === 'delivered' && <Ionicons name="checkmark-done" size={12} color="#E3F2FD" />}
                {message.status === 'error' && (
                  <Pressable onPress={() => handleRetryMessage(message)}>
                    <Ionicons name="refresh" size={12} color="#FF6B6B" />
                  </Pressable>
                )}
              </XStack>
            )}
          </XStack>
        </Card>

        {message.error && (
          <Text
            color="$red10"
            fontSize="$2"
            marginTop="$1"
            fontStyle="italic"
          >
            {message.error} • Tap to retry
          </Text>
        )}
      </YStack>

      {message.isUser && (
        <Avatar circular size="$3" marginLeft="$2" marginTop="$1" borderWidth={1} borderColor="$blue6">
          <AvatarFallback backgroundColor="$blue10">
            <Ionicons name="person" size={16} color="white" />
          </AvatarFallback>
        </Avatar>
      )}
    </XStack>
  );

  return (
    <KeyboardAvoidingView 
      style={{ flex: 1 }} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <YStack flex={1} backgroundColor="$background">
        {/* Enhanced Professional Header */}
        <XStack
          backgroundColor="$purple10"
          padding="$4"
          paddingTop="$6"
          alignItems="center"
          justifyContent="space-between"
          borderBottomWidth={1}
          borderBottomColor="$purple8"
        >
          <XStack alignItems="center" gap="$3">
            <Avatar circular size="$4" borderWidth={2} borderColor="white">
              <AvatarFallback backgroundColor="white">
                <Ionicons name="sparkles" size={20} color="#7529B3" />
              </AvatarFallback>
            </Avatar>
            <YStack>
              <H3 color="white" fontSize="$6" fontWeight="700">BolTalab AI Assistant</H3>
              <XStack alignItems="center" gap="$2">
                <YStack
                  width={8}
                  height={8}
                  borderRadius={4}
                  backgroundColor={isOnline ? "$green9" : "$red9"}
                />
                <Text color="$purple2" fontSize="$3" fontWeight="500">
                  {isTyping ? '✨ Thinking...' : isOnline ? '🟢 Online • Ready to help' : '🔴 Offline • Limited functionality'}
                </Text>
              </XStack>
            </YStack>
          </XStack>
          <XStack gap="$2">
            <Button
              circular
              size="$3"
              backgroundColor="rgba(255,255,255,0.2)"
              onPress={() => navigateAndClose('/(customer-pages)/home')}
            >
              <Ionicons name="home" size={18} color="white" />
            </Button>
            <Button
              circular
              size="$3"
              backgroundColor="$purple8"
              onPress={() => setShowQuickActions(!showQuickActions)}
            >
              <Ionicons name="apps" size={18} color="white" />
            </Button>
          </XStack>
        </XStack>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          flex={1}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 16 }}
        >
          {Array.isArray(messages) && messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {isTyping && (
            <XStack justifyContent="flex-start" paddingHorizontal="$4" marginBottom="$3">
              <Avatar circular size="$3" marginRight="$2" marginTop="$1">
                <AvatarFallback backgroundColor="$purple10">
                  <Ionicons name="chatbubble-ellipses" size={16} color="white" />
                </AvatarFallback>
              </Avatar>
              <Card backgroundColor="$gray2" padding="$3" borderRadius="$4">
                <XStack alignItems="center" gap="$2">
                  <Spinner size="small" color="$purple10" />
                  <Text color="$gray10" fontSize="$3">AI is typing...</Text>
                </XStack>
              </Card>
            </XStack>
          )}
        </ScrollView>

        {/* Enhanced Quick Actions */}
        {showQuickActions && Array.isArray(messages) && messages.length <= 1 && (
          <YStack padding="$4" backgroundColor="$gray1" borderTopWidth={1} borderTopColor="$gray4">
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$4">
              <Text fontSize="$6" fontWeight="700" color="$gray12">
                🚀 {t('chat.quickActions', { defaultValue: 'Quick Actions' })}
              </Text>
              <Button
                size="$2"
                circular
                backgroundColor="$gray4"
                onPress={() => setShowQuickActions(false)}

              >
                <Ionicons name="close" size={14} color="$gray10" />
              </Button>
            </XStack>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} marginBottom="$4">
              <XStack gap="$3" paddingHorizontal="$1">
                {quickActions.map((action) => (
                  <Button
                    key={action.id}
                    size="$4"
                    backgroundColor={action.color as any}
                    borderRadius="$6"
                    onPress={() => handleQuickAction(action.action, action.label)}
                    minWidth={110}
                    height={80}

                  >
                    <YStack alignItems="center" gap="$2">
                      <Ionicons name={action.icon as any} size={24} color="white" />
                      <Text color="white" fontSize="$3" fontWeight="600" textAlign="center" numberOfLines={2}>
                        {action.label}
                      </Text>
                    </YStack>
                  </Button>
                ))}
              </XStack>
            </ScrollView>

            <Separator marginVertical="$4" backgroundColor="$gray6" />

            <Text fontSize="$5" fontWeight="700" marginBottom="$3" color="$gray12">
              💡 {t('chat.smartSuggestions.title', { defaultValue: 'Smart Suggestions' })}
            </Text>
            <YStack gap="$3">
              {getContextualSuggestions().slice(0, 4).map((question, index) => (
                <Pressable
                  key={index}
                  onPress={() => handleSuggestedQuestion(question)}
                >
                  <Card
                    backgroundColor="white"
                    padding="$4"
                    borderRadius="$4"
                    borderWidth={1}
                    borderColor="$gray5"

                  >
                    <XStack alignItems="center" gap="$3">
                      <YStack
                        width={6}
                        height={6}
                        borderRadius={3}
                        backgroundColor="$purple8"
                      />
                      <Text color="$gray12" fontSize="$4" fontWeight="500" flex={1}>
                        {question}
                      </Text>
                      <Ionicons name="chevron-forward" size={16} color="#999" />
                    </XStack>
                  </Card>
                </Pressable>
              ))}
            </YStack>
          </YStack>
        )}

        {/* Enhanced Professional Input Area */}
        <YStack
          backgroundColor="white"
          borderTopWidth={1}
          borderTopColor="$gray4"
        >
          <XStack
            padding="$4"
            alignItems="flex-end"
            gap="$3"
          >
            <YStack flex={1}>
              <Input
                flex={1}
                placeholder={t('chat.typeMessage', { defaultValue: '✨ Ask me anything about BolTalab services...' })}
                placeholderTextColor="$gray8"
                value={inputText}
                onChangeText={setInputText}
                multiline
                maxHeight={120}
                borderRadius="$6"
                backgroundColor="$gray1"
                borderWidth={2}
                borderColor={inputText.trim() ? "$purple6" : "$gray5"}
                paddingHorizontal="$4"
                paddingVertical="$4"
                fontSize="$4"
                fontWeight="400"
                onSubmitEditing={() => handleSendMessage(inputText)}
                focusStyle={{
                  borderColor: "$purple8",
                  backgroundColor: "white"
                }}
              />

              {/* Character count and suggestions */}
              <XStack justifyContent="space-between" alignItems="center" marginTop="$2">
                <Text color="$gray8" fontSize="$2">
                  {inputText.length > 0 && `${inputText.length} ${t('chat.characters', { defaultValue: 'characters' })}`}
                </Text>
                <XStack gap="$2">
                  <Button
                    size="$2"
                    backgroundColor="$gray4"
                    borderRadius="$4"
                    onPress={() => setShowQuickActions(!showQuickActions)}
                  >
                    <Ionicons name="apps" size={14} color="$gray10" />
                  </Button>
                </XStack>
              </XStack>
            </YStack>

            <Button
              circular
              size="$5"
              backgroundColor={inputText.trim() && !isTyping ? "$purple10" : "$gray6"}
              disabled={!inputText.trim() || isTyping}
              onPress={() => handleSendMessage(inputText)}
              borderWidth={inputText.trim() ? 0 : 1}
              borderColor="$gray5"
            >
              {isTyping ? (
                <Spinner size="small" color="white" />
              ) : (
                <Ionicons
                  name={inputText.trim() ? "send" : "send-outline"}
                  size={22}
                  color="white"
                />
              )}
            </Button>
          </XStack>
        </YStack>
      </YStack>
    </KeyboardAvoidingView>
  );
};
