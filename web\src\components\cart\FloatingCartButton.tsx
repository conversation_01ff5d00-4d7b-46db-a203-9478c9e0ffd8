import React, { useState } from 'react';
import { Shopping<PERSON>art, ChevronLeft } from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { motion, AnimatePresence } from 'framer-motion';
import CartModal from './CartModal';

const FloatingCartButton: React.FC = () => {
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { getTotalItems, getTotalPrice } = useCartStore();

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  if (totalItems === 0) {
    return null;
  }

  return (
    <>
      <motion.div
        initial={{ x: 100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 100, opacity: 0 }}
        className="fixed top-1/4 right-0 transform -translate-y-1/2 z-40 flex items-center"
      >
        {/* Animated Arrow Indicator */}
        <motion.div
          animate={{ x: [-6, -10, -6] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="mr-2"
        >
          <ChevronLeft className="w-5 h-5 text-purple-600" />
        </motion.div>

        {/* Vertical Cart Button */}
        <button
          onClick={() => setIsCartOpen(true)}
          className="bg-gradient-to-b from-purple-600 to-purple-700 text-white py-4 px-3 rounded-l-2xl shadow-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-300 flex flex-col items-center gap-2 min-h-[100px] group"
        >
          <div className="relative">
            <ShoppingCart className="w-5 h-5" />
            {totalItems > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {totalItems > 99 ? '99+' : totalItems}
              </span>
            )}
          </div>

          <div className="text-center">
            <div className="text-sm font-medium">
              {totalItems}
            </div>
            <div className="text-xs opacity-90">
              ₪{totalPrice.toFixed(2)}
            </div>
          </div>

          {/* Hover indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
            className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-12 bg-white/30 rounded-r-full"
          />
        </button>
      </motion.div>

      <CartModal
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
      />
    </>
  );
};

export default FloatingCartButton;
