import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Truck, Package, MapPin, Star, Sparkles, Zap, Crown,
  Heart, Gift, Bot, Shield, Gem, TrendingUp, Flame
} from 'lucide-react';
import Logo from '../common/Logo';

const AuthLayout: React.FC = () => {
  const floatingElements = [
    { icon: Truck, delay: 0, x: 15, y: 20, color: 'text-blue-400' },
    { icon: Package, delay: 0.5, x: 85, y: 15, color: 'text-orange-400' },
    { icon: MapPin, delay: 1, x: 10, y: 80, color: 'text-green-400' },
    { icon: Star, delay: 1.5, x: 90, y: 85, color: 'text-yellow-400' },
    { icon: Bot, delay: 2, x: 20, y: 60, color: 'text-purple-400' },
    { icon: Gift, delay: 2.5, x: 80, y: 50, color: 'text-pink-400' },
    { icon: Crown, delay: 3, x: 50, y: 10, color: 'text-primary-400' },
    { icon: Gem, delay: 3.5, x: 5, y: 40, color: 'text-cyan-400' },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Spectacular Multi-Dimensional Background */}
      <div className="fixed inset-0">
        {/* Simplified Static Gradient */}
        <div
          className="absolute inset-0"
          style={{
            background: 'linear-gradient(135deg, #0f172a 0%, #1e1b4b 25%, #581c87 50%, #7c2d12 75%, #0f172a 100%)'
          }}
        />

        {/* Simplified Static Mesh Overlay */}
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at 30% 40%, rgba(117, 41, 179, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 60%, rgba(103, 179, 41, 0.25) 0%, transparent 50%), radial-gradient(circle at 50% 80%, rgba(143, 61, 210, 0.2) 0%, transparent 50%)'
          }}
        />

        {/* Simplified Static Orbs */}
        <div className="absolute -top-40 -left-40 w-[300px] h-[300px] bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -right-40 w-[280px] h-[280px] bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl" />
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[250px] h-[250px] bg-gradient-to-br from-secondary-500/15 to-fourth-500/15 rounded-full blur-3xl"
        />

        {/* Simplified Floating Particles System */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute rounded-full will-change-transform"
            style={{
              left: `${20 + (i * 10)}%`,
              top: `${20 + (i * 8)}%`,
              width: '4px',
              height: '4px',
              background: i % 3 === 0 ? '#7529B3' :
                         i % 3 === 1 ? '#67B329' :
                         '#8F3DD2',
              opacity: 0.3,
            }}
            animate={{
              y: [-10, -30, -10],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Minimal Cosmic Dust */}
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={`dust-${i}`}
            className="absolute w-px h-px bg-white/15 rounded-full will-change-transform"
            style={{
              left: `${10 + (i * 8)}%`,
              top: `${15 + (i * 7)}%`,
            }}
            animate={{
              y: [-8, -20, -8],
              opacity: [0, 0.4, 0],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              delay: i * 1.2,
              ease: "linear"
            }}
          />
        ))}

        {/* Minimal Shooting Stars */}
        {[...Array(2)].map((_, i) => (
          <motion.div
            key={`star-${i}`}
            className="absolute will-change-transform opacity-30"
            style={{
              left: `${30 + i * 40}%`,
              top: `${25 + i * 30}%`,
              width: '1px',
              height: '15px',
              background: 'linear-gradient(to bottom, transparent, #ffffff, transparent)',
              transform: `rotate(-45deg)`
            }}
            animate={{
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 15,
              delay: i * 8,
              ease: "easeOut"
            }}
          />
        ))}

        {/* Simplified Floating Service Icons */}
        {floatingElements.slice(0, 4).map((element, index) => (
          <motion.div
            key={`service-icon-${index}`}
            className="absolute will-change-transform opacity-10"
            style={{ left: `${element.x}%`, top: `${element.y}%` }}
            animate={{
              y: [-8, 8, -8],
              opacity: [0.08, 0.15, 0.08],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              delay: element.delay * 2,
              ease: "easeInOut"
            }}
          >
            <element.icon size={24} className={element.color} />
          </motion.div>
        ))}

        {/* Minimal Static Geometric Elements */}
        {[...Array(3)].map((_, i) => (
          <div
            key={`geo-${i}`}
            className="absolute opacity-10"
            style={{
              left: `${20 + (i * 30)}%`,
              top: `${25 + (i * 25)}%`,
              width: '20px',
              height: '20px',
              background: `rgba(117, 41, 179, 0.1)`,
              borderRadius: i % 2 === 0 ? '50%' : '25%',
              filter: 'blur(1px)'
            }}
          />
        ))}

        {/* Static Nebula Clouds */}
        <div className="absolute top-1/4 left-1/4 w-[150px] h-[80px] rounded-full blur-3xl opacity-10 bg-gradient-to-r from-primary-500/20 to-secondary-500/20" />
        <div className="absolute bottom-1/3 right-1/3 w-[120px] h-[60px] rounded-full blur-3xl opacity-8 bg-gradient-to-r from-third-500/15 to-primary-500/15" />
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-lg"
        >
          {/* Simplified Glass Container */}
          <div className="relative">
            {/* Static Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/15 to-secondary-500/15 rounded-3xl blur-xl opacity-40" />

            <div className="relative bg-white/90 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/30 p-8 overflow-hidden">
              {/* Subtle Pattern Overlay */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, #7529B3 2px, transparent 2px),
                                   radial-gradient(circle at 75% 75%, #67B329 2px, transparent 2px)`,
                  backgroundSize: '50px 50px'
                }} />
              </div>

              {/* Logo Section */}
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center mb-8 relative z-10"
              >
                <div className="flex justify-center mb-4">
                  <Logo size="lg" />
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="relative z-10"
              >
                <Outlet />
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
