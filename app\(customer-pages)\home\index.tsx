import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { CustomerScreenContent } from '~/components/customer-pages-components/CustomerScreenContent';
import { ChatNavigationButton } from '~/components/customer-pages-components/ChatNavigationButton';

export default function CustomerHome() {
  return (
    <>
      <Container style={{ width: '100%', padding: 0 }}>
        <CustomerScreenContent path="app/(customer-pages)/home/<USER>" title="CustomerHome"></CustomerScreenContent>

        {/* Floating AI Chat Button */}
        <ChatNavigationButton
          variant="floating"
          size="medium"
          showText={true}
        />
      </Container>
    </>
  );
}