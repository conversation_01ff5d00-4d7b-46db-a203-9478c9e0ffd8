import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Location = {
  lat: number;
  lng: number;
  address?: string;
};

export type OrderItem = {
  product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
  };
  qty: number;
  finalPrice: number;
  selectedAdditions?: Array<{ id: string; name: string; price: number }>;
  selectedSides?: Array<{ id: string; name: string; price: number }>;
  without?: string[];
  selectedSize?: string;
  selectedColor?: string;
};

export type Order = {
  id: string;
  createdAt: string;
  items: OrderItem[];
  supplier: {
    id: string;
    name: string;
  };
  subtotal: number;
  deliveryFee: number;
  promoDiscount: number;
  total: number;
  status: 'Pending' | 'Confirmed' | 'Preparing' | 'Ready' | 'On the Way' | 'Delivered' | 'Cancelled';
  address: string;
  phone: string;
  notes?: string;
  paymentMethod: 'cash' | 'card';
  promoCode?: string;
  estimatedTime: string;
  placedAt: string;
  driverName?: string;
  driverPhone?: string;
  driverLocation?: Location;
  deliveryLocation?: Location;
};

type OrdersState = {
  orders: Order[];
  addOrder: (order: Order) => void;
  addMultipleOrders: (orders: Order[]) => void;
  updateOrderStatus: (orderId: string, status: Order['status']) => void;
  getOrderById: (orderId: string) => Order | undefined;
  getOrdersByStatus: (status: Order['status']) => Order[];
  clearOrders: () => void;
};

// Mock orders for development - including supplier-specific orders
const mockOrders: Order[] = [
  {
    id: 'ORD_001',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    items: [
      {
        product: {
          id: 'shawerma-roll-1',
          name: 'لفة شاورما لحم',
          image: 'https://scontent.fjrs26-1.fna.fbcdn.net/v/t39.30808-6/470506779_1088072999671074_8779765893442442044_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_cat=111&ccb=1-7&_nc_sid=127cfc&_nc_ohc=Ej8Ej8Ej8Ej8Q7kNvwGzuYBh&_nc_oc=AdmIA3redeqcoT05okH2NmNSjSYeT_uv1jn7LhnRnEkmkObiVBzMboKEzf_k-3I-v4A&_nc_zt=23&_nc_ht=scontent.fjrs26-1.fna&_nc_gid=9B-N8eOhB9MsrfwA1YE7GA&oh=00_AfPEiE9ReyhXSsqImZmRF5_gy4rpPu4RKjwmmY6ZTieXgA&oe=685E1C5A',
          price: 15,
          category: 'Main Dishes'
        },
        qty: 2,
        finalPrice: 30
      },
      {
        product: {
          id: 'shawerma-plate-1',
          name: 'صحن شاورما لحم',
          image: 'https://scontent.fjrs26-1.fna.fbcdn.net/v/t39.30808-6/470506779_1088072999671074_8779765893442442044_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_cat=111&ccb=1-7&_nc_sid=127cfc&_nc_ohc=Ej8Ej8Ej8Ej8Q7kNvwGzuYBh&_nc_oc=AdmIA3redeqcoT05okH2NmNSjSYeT_uv1jn7LhnRnEkmkObiVBzMboKEzf_k-3I-v4A&_nc_zt=23&_nc_ht=scontent.fjrs26-1.fna&_nc_gid=9B-N8eOhB9MsrfwA1YE7GA&oh=00_AfPEiE9ReyhXSsqImZmRF5_gy4rpPu4RKjwmmY6ZTieXgA&oe=685E1C5A',
          price: 25,
          category: 'Main Dishes'
        },
        qty: 1,
        finalPrice: 25
      }
    ],
    supplier: {
      id: '3a-kefak',
      name: '3a kefak'
    },
    subtotal: 55,
    deliveryFee: 5,
    promoDiscount: 0,
    total: 60,
    status: 'Pending',
    address: 'شارع فيصل، وسط البلد، نابلس',
    phone: '+970591234567',
    paymentMethod: 'cash',
    estimatedTime: '25-35 mins',
    placedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    notes: 'Extra spicy please! 🌶️',
    driverName: 'Ahmad الطيار',
    driverPhone: '+970599876543',
    driverLocation: {
      lat: 32.2200,
      lng: 35.2530,
      address: 'Near Al-Najah University'
    },
    deliveryLocation: {
      lat: 32.2211,
      lng: 35.2544,
      address: 'شارع فيصل، وسط البلد، نابلس'
    }
  },
  {
    id: 'ORD_002',
    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
    items: [
      {
        product: {
          id: 'fish-fillet-1',
          name: 'Zemrcado Fish Fillet',
          image: 'https://scontent.fjrs26-1.fna.fbcdn.net/v/t39.30808-6/496716187_706414032024307_5972760020554710225_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_cat=101&ccb=1-7&_nc_sid=127cfc&_nc_ohc=8zRf6o2HshcQ7kNvwGzuYBh&_nc_oc=AdmIA3redeqcoT05okH2NmNSjSYeT_uv1jn7LhnRnEkmkObiVBzMboKEzf_k-3I-v4A&_nc_zt=23&_nc_ht=scontent.fjrs26-1.fna&_nc_gid=9B-N8eOhB9MsrfwA1YE7GA&oh=00_AfPEiE9ReyhXSsqImZmRF5_gy4rpPu4RKjwmmY6ZTieXgA&oe=685E1C5A',
          price: 20,
          category: 'Sea Food'
        },
        qty: 1,
        finalPrice: 20
      }
    ],
    supplier: {
      id: 'zemrcado',
      name: 'Zemrcado'
    },
    subtotal: 20,
    deliveryFee: 5,
    promoDiscount: 0,
    total: 25,
    status: 'Preparing',
    address: 'جامعة النجاح الوطنية، نابلس',
    phone: '+970591234568',
    paymentMethod: 'card',
    estimatedTime: '30-40 mins',
    placedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
    driverName: 'Mohammed Ali',
    driverPhone: '+970599876544'
  },
  {
    id: 'ORD_003',
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
    items: [
      {
        product: {
          id: 'shawerma-roll-1',
          name: 'لفة شاورما لحم',
          image: 'https://scontent.fjrs26-1.fna.fbcdn.net/v/t39.30808-6/470506779_1088072999671074_8779765893442442044_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_cat=111&ccb=1-7&_nc_sid=127cfc&_nc_ohc=Ej8Ej8Ej8Ej8Q7kNvwGzuYBh&_nc_oc=AdmIA3redeqcoT05okH2NmNSjSYeT_uv1jn7LhnRnEkmkObiVBzMboKEzf_k-3I-v4A&_nc_zt=23&_nc_ht=scontent.fjrs26-1.fna&_nc_gid=9B-N8eOhB9MsrfwA1YE7GA&oh=00_AfPEiE9ReyhXSsqImZmRF5_gy4rpPu4RKjwmmY6ZTieXgA&oe=685E1C5A',
          price: 15,
          category: 'Main Dishes'
        },
        qty: 3,
        finalPrice: 45
      }
    ],
    supplier: {
      id: '3a-kefak',
      name: '3a kefak'
    },
    subtotal: 45,
    deliveryFee: 5,
    promoDiscount: 0,
    total: 50,
    status: 'On the Way',
    address: 'البلدة القديمة، نابلس',
    phone: '+970591234569',
    paymentMethod: 'cash',
    estimatedTime: '20-30 mins',
    placedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    driverName: 'Khalil Omar',
    driverPhone: '+970599876545'
  },
  {
    id: 'ORD_004',
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
    items: [
      {
        product: {
          id: 'shawerma-plate-1',
          name: 'صحن شاورما لحم',
          image: 'https://scontent.fjrs26-1.fna.fbcdn.net/v/t39.30808-6/470506779_1088072999671074_8779765893442442044_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_cat=111&ccb=1-7&_nc_sid=127cfc&_nc_ohc=Ej8Ej8Ej8Ej8Q7kNvwGzuYBh&_nc_oc=AdmIA3redeqcoT05okH2NmNSjSYeT_uv1jn7LhnRnEkmkObiVBzMboKEzf_k-3I-v4A&_nc_zt=23&_nc_ht=scontent.fjrs26-1.fna&_nc_gid=9B-N8eOhB9MsrfwA1YE7GA&oh=00_AfPEiE9ReyhXSsqImZmRF5_gy4rpPu4RKjwmmY6ZTieXgA&oe=685E1C5A',
          price: 25,
          category: 'Main Dishes'
        },
        qty: 2,
        finalPrice: 50
      }
    ],
    supplier: {
      id: '3a-kefak',
      name: '3a kefak'
    },
    subtotal: 50,
    deliveryFee: 5,
    promoDiscount: 5,
    total: 50,
    status: 'Delivered',
    address: 'مستشفى رفيديا، نابلس',
    phone: '+970591234570',
    paymentMethod: 'card',
    estimatedTime: '25-35 mins',
    placedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    driverName: 'Samer Ahmad',
    driverPhone: '+970599876546'
  },
  {
    id: 'ORD_1752326131055_def456',
    createdAt: '2025-01-12T14:15:00Z',
    items: [
      {
        product: {
          id: 'margherita-pizza-1',
          name: 'Margherita Pizza',
          image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop',
          price: 40,
          category: 'main'
        },
        qty: 1,
        finalPrice: 45
      },
      {
        product: {
          id: 'coca-cola-1',
          name: 'Coca Cola',
          image: 'https://images.unsplash.com/photo-1581636625402-29b2a704ef13?w=400&h=400&fit=crop',
          price: 4,
          category: 'drinks'
        },
        qty: 2,
        finalPrice: 8
      }
    ],
    supplier: {
      id: 'SUP_124',
      name: 'Pizza Palace'
    },
    subtotal: 48,
    deliveryFee: 10,
    promoDiscount: 5,
    total: 53,
    status: 'On the Way',
    address: 'Ramallah, Palestine',
    phone: '+970568406041',
    paymentMethod: 'card',
    estimatedTime: '25-35 mins',
    placedAt: '2025-01-12T14:15:00Z',
    driverName: 'Mohammed Ali',
    driverPhone: '+970599876544',
    driverLocation: {
      lat: 32.2190,
      lng: 35.2490,
      address: 'Almost there!'
    },
    deliveryLocation: {
      lat: 32.2180,
      lng: 35.2480,
      address: 'Ramallah, Palestine'
    }
  },
  {
    id: 'ORD_1752326131056_ghi789',
    createdAt: '2025-01-12T16:45:00Z',
    items: [
      {
        product: {
          id: 'burger-deluxe-1',
          name: 'Deluxe Burger',
          image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop',
          price: 28,
          category: 'main'
        },
        qty: 1,
        finalPrice: 30
      }
    ],
    supplier: {
      id: 'SUP_125',
      name: 'Burger House'
    },
    subtotal: 30,
    deliveryFee: 8,
    promoDiscount: 0,
    total: 38,
    status: 'Preparing',
    address: 'Jerusalem, Palestine',
    phone: '+970568406041',
    paymentMethod: 'cash',
    estimatedTime: '40-50 mins',
    placedAt: '2025-01-12T16:45:00Z',
    driverName: 'Khalil Omar',
    driverPhone: '0595956016',
    driverLocation: {
      lat: 32.2240,
      lng: 35.2580,
      address: 'On the way to customer'
    },
    deliveryLocation: {
      lat: 32.2250,
      lng: 35.2600,
      address: 'Jerusalem, Palestine'
    }
  }
];

export const useOrdersStore = create<OrdersState>()(
  persist(
    (set, get) => ({
      orders: mockOrders,

      addOrder: (order) => {
        set((state) => ({
          orders: [order, ...state.orders] // Add new order at the beginning
        }));
      },

      addMultipleOrders: (orders) => {
        set((state) => ({
          orders: [...orders, ...state.orders] // Add new orders at the beginning
        }));
      },

      updateOrderStatus: (orderId, status) => {
        set((state) => ({
          orders: state.orders.map((order) =>
            order.id === orderId ? { ...order, status } : order
          )
        }));
      },

      getOrderById: (orderId) => {
        return get().orders.find((order) => order.id === orderId);
      },

      getOrdersByStatus: (status) => {
        return get().orders.filter((order) => order.status === status);
      },

      clearOrders: () => {
        set({ orders: [] });
      }
    }),
    {
      name: 'wasel-orders-storage', // unique name for localStorage
    }
  )
);
