import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Clock, MapPin, Phone, User, Package, CheckCircle, Truck, Navigation, Zap, Star, Activity, Route, Timer, Shield } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ile<PERSON><PERSON>er, <PERSON><PERSON>, Popup } from 'react-leaflet';
import { motion, AnimatePresence } from 'framer-motion';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const PackageTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const packageId = searchParams.get('packageId');
  const pickupId = searchParams.get('pickupId');

  // Scroll animation states
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Mock package data
  const packageData = {
    id: packageId || pickupId || 'PK-123456',
    type: packageId ? 'sent' : 'pickup',
    fromAddress: 'Nablus, Palestine',
    fromLocation: { lat: 32.2211, lng: 35.2544 },
    toAddress: 'Ramallah, Palestine',
    toLocation: { lat: 31.9038, lng: 35.2034 },
    senderName: 'Ahmad Samer',
    senderPhone: '+970568406041',
    receiverName: 'Sara Ahmad',
    receiverPhone: '+970599123456',
    driverName: 'Omar Khalil',
    driverPhone: '0599887766',
    driverLocation: { lat: 32.1000, lng: 35.1500 }, // Between pickup and delivery
    status: 'In Transit',
    estimatedDelivery: '2-3 hours',
    packageType: 'Documents',
    packageSize: 'Small',
    packageWeight: '500g',
    price: 15,
    createdAt: new Date().toISOString()
  };

  // Package status timeline
  const statusTimeline = [
    { 
      status: packageData.type === 'pickup' ? 'Pickup Requested' : 'Package Sent', 
      time: '09:30 AM', 
      completed: true, 
      description: packageData.type === 'pickup' ? 'Pickup request has been placed' : 'Package has been sent for delivery'
    },
    { 
      status: 'Driver Assigned', 
      time: '09:45 AM', 
      completed: true, 
      description: 'A driver has been assigned to your package' 
    },
    { 
      status: packageData.type === 'pickup' ? 'Package Picked Up' : 'Package Collected', 
      time: '10:15 AM', 
      completed: true, 
      description: packageData.type === 'pickup' ? 'Package has been picked up from sender' : 'Package has been collected for delivery'
    },
    { 
      status: 'In Transit', 
      time: '10:30 AM', 
      completed: true, 
      description: 'Package is on the way to destination' 
    },
    { 
      status: 'Out for Delivery', 
      time: 'ETA 12:00 PM', 
      completed: false, 
      description: 'Package is out for final delivery' 
    },
    { 
      status: 'Delivered', 
      time: 'ETA 12:30 PM', 
      completed: false, 
      description: 'Package will be delivered to recipient' 
    }
  ];

  // Handle scroll for header animation with ultra-smooth debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCallDriver = () => {
    window.open(`tel:${packageData.driverPhone}`);
  };

  const handleCallSender = () => {
    window.open(`tel:${packageData.senderPhone}`);
  };

  const handleCallReceiver = () => {
    window.open(`tel:${packageData.receiverPhone}`);
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-primary-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-500/30 to-secondary-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-secondary-500/30 to-third-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-orange-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Main Header */}
        <div className="relative z-10 bg-white/10 backdrop-blur-md border-b border-white/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center gap-4">
              <motion.button
                onClick={() => navigate(-1)}
                className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="w-6 h-6 text-white group-hover:text-primary-200 transition-colors" />
              </motion.button>
              <div>
                <motion.h1
                  className="text-3xl font-bold text-white"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  Track Package
                </motion.h1>
                <motion.p
                  className="text-white/80 text-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  Package #{packageData.id}
                </motion.p>
              </div>
            </div>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "-100px",
            zIndex: isHeaderCompact ? 50 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-white">Track Package</h1>
                <p className="text-white/70 text-sm">#{packageData.id}</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Package Info */}
            <div className="space-y-6">
              {/* Current Status - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center gap-4 mb-6">
                  <motion.div
                    className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"
                    animate={{
                      scale: [1, 1.05, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Truck className="w-8 h-8 text-white" />
                  </motion.div>
                  <div>
                    <motion.h2
                      className="text-2xl font-bold text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                    >
                      {packageData.status}
                    </motion.h2>
                    <motion.p
                      className="text-white/80 text-lg"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      ETA: {packageData.estimatedDelivery}
                    </motion.p>
                  </div>
                </div>

                <motion.div
                  className="bg-gradient-to-r from-primary-500/20 to-secondary-500/20 border border-primary-400/30 rounded-xl p-4 backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <div className="flex items-center gap-3">
                    <motion.div
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.7, 1, 0.7]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Activity className="w-5 h-5 text-secondary-400" />
                    </motion.div>
                    <p className="text-white font-medium">
                      Your package is on the way! {packageData.driverName} is delivering it.
                    </p>
                  </div>
                </motion.div>
              </motion.div>

              {/* Package Details - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-third-500 to-primary-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Package className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Package Details</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {[
                    { label: 'Type', value: packageData.packageType, icon: Package },
                    { label: 'Size', value: packageData.packageSize, icon: Shield },
                    { label: 'Weight', value: packageData.packageWeight, icon: Timer },
                    { label: 'Service Fee', value: `₪${packageData.price}`, icon: Star }
                  ].map((item, index) => (
                    <motion.div
                      key={item.label}
                      className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <item.icon className="w-4 h-4 text-primary-400" />
                        <p className="font-medium text-white/90 text-sm">{item.label}</p>
                      </div>
                      <p className="text-white font-semibold">{item.value}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Contact Information - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-secondary-500 to-third-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Phone className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Contact Information</h3>
                </div>

                <div className="space-y-4">
                  {/* Driver */}
                  <motion.div
                    className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <User className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="font-bold text-white">{packageData.driverName}</h4>
                      <p className="text-white/70 text-sm">Driver</p>
                    </div>
                    <motion.button
                      onClick={handleCallDriver}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 text-sm font-medium shadow-lg"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Phone className="w-4 h-4" />
                      Call
                    </motion.button>
                  </motion.div>

                  {/* Sender */}
                  <motion.div
                    className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <User className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="font-bold text-white">{packageData.senderName}</h4>
                      <p className="text-white/70 text-sm">Sender</p>
                    </div>
                    <motion.button
                      onClick={handleCallSender}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white rounded-xl hover:from-secondary-600 hover:to-secondary-700 transition-all duration-300 text-sm font-medium shadow-lg"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Phone className="w-4 h-4" />
                      Call
                    </motion.button>
                  </motion.div>

                  {/* Receiver */}
                  <motion.div
                    className="flex items-center gap-4 bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1 }}
                    >
                      <User className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="font-bold text-white">{packageData.receiverName}</h4>
                      <p className="text-white/70 text-sm">Receiver</p>
                    </div>
                    <motion.button
                      onClick={handleCallReceiver}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-300 text-sm font-medium shadow-lg"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Phone className="w-4 h-4" />
                      Call
                    </motion.button>
                  </motion.div>
                </div>
              </motion.div>

              {/* Package Timeline - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-third-500 to-secondary-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Route className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Package Timeline</h3>
                </div>

                <div className="space-y-6">
                  {statusTimeline.map((step, index) => (
                    <motion.div
                      key={index}
                      className="flex items-start gap-4"
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <motion.div
                        className={`w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg relative ${
                          step.completed
                            ? 'bg-gradient-to-br from-secondary-500 to-secondary-600'
                            : 'bg-white/10 border border-white/20'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        animate={step.completed ? {
                          scale: [1, 1.1, 1],
                        } : {}}
                        transition={{
                          duration: 2,
                          repeat: step.completed ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        {step.completed ? (
                          <CheckCircle className="w-6 h-6 text-white" />
                        ) : (
                          <Clock className="w-6 h-6 text-white/60" />
                        )}
                        {step.completed && (
                          <motion.div
                            className="absolute inset-0 rounded-2xl bg-secondary-400/30"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 0, 0.5]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                        )}
                      </motion.div>
                      <div className="flex-1 bg-white/5 rounded-xl p-4 border border-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className={`font-bold ${
                            step.completed ? 'text-white' : 'text-white/60'
                          }`}>
                            {step.status}
                          </h4>
                          <span className={`text-sm font-medium px-3 py-1 rounded-lg ${
                            step.completed
                              ? 'bg-secondary-500/20 text-secondary-300 border border-secondary-400/30'
                              : 'bg-white/10 text-white/60 border border-white/20'
                          }`}>
                            {step.time}
                          </span>
                        </div>
                        <p className={`text-sm ${
                          step.completed ? 'text-white/80' : 'text-white/50'
                        }`}>
                          {step.description}
                        </p>
                      </div>
                      {/* Connecting line */}
                      {index < statusTimeline.length - 1 && (
                        <div className="absolute left-6 top-16 w-0.5 h-6 bg-gradient-to-b from-white/30 to-white/10" />
                      )}
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Right Column - Map */}
            <div className="space-y-6">
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="p-6 border-b border-white/20">
                  <div className="flex items-center gap-3 mb-2">
                    <motion.div
                      className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center"
                      animate={{
                        scale: [1, 1.1, 1],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Navigation className="w-5 h-5 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold text-white">Live Tracking</h3>
                  </div>
                  <p className="text-white/80">Follow your package's journey in real-time</p>
                </div>

                <div className="h-96 relative">
                  <MapContainer
                    center={[packageData.driverLocation.lat, packageData.driverLocation.lng]}
                    zoom={10}
                    style={{ height: '100%', width: '100%' }}
                  >
                    <TileLayer
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    />

                    {/* Pickup Location Marker */}
                    <Marker position={[packageData.fromLocation.lat, packageData.fromLocation.lng]}>
                      <Popup>
                        <div className="text-center">
                          <strong>Pickup Location</strong>
                          <br />
                          <span className="text-sm text-gray-600">{packageData.fromAddress}</span>
                        </div>
                      </Popup>
                    </Marker>

                    {/* Driver Marker */}
                    <Marker position={[packageData.driverLocation.lat, packageData.driverLocation.lng]}>
                      <Popup>
                        <div className="text-center">
                          <strong>{packageData.driverName}</strong>
                          <br />
                          <span className="text-sm text-gray-600">Your Driver</span>
                        </div>
                      </Popup>
                    </Marker>

                    {/* Delivery Location Marker */}
                    <Marker position={[packageData.toLocation.lat, packageData.toLocation.lng]}>
                      <Popup>
                        <div className="text-center">
                          <strong>Delivery Address</strong>
                          <br />
                          <span className="text-sm text-gray-600">{packageData.toAddress}</span>
                        </div>
                      </Popup>
                    </Marker>
                  </MapContainer>

                  {/* Live indicator overlay */}
                  <motion.div
                    className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center gap-2 shadow-lg"
                    animate={{
                      scale: [1, 1.05, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-white rounded-full"
                      animate={{
                        opacity: [1, 0.3, 1],
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                    LIVE
                  </motion.div>
                </div>
              </motion.div>

              {/* Addresses - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <motion.div
                    className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <MapPin className="w-5 h-5 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white">Addresses</h3>
                </div>

                <div className="space-y-4">
                  <motion.div
                    className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center mt-1 shadow-lg"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </motion.div>
                    <div>
                      <p className="font-bold text-white mb-1">From</p>
                      <p className="text-white/80">{packageData.fromAddress}</p>
                    </div>
                  </motion.div>

                  <motion.div
                    className="flex items-start gap-4 bg-white/5 rounded-xl p-4 border border-white/10"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mt-1 shadow-lg"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 0.5
                      }}
                    >
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </motion.div>
                    <div>
                      <p className="font-bold text-white mb-1">To</p>
                      <p className="text-white/80">{packageData.toAddress}</p>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Map Legend - Enhanced */}
              <motion.div
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <motion.div
                    className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Zap className="w-4 h-4 text-white" />
                  </motion.div>
                  <h4 className="font-bold text-white">Map Legend</h4>
                </div>
                <div className="space-y-3">
                  {[
                    { color: 'bg-secondary-500', label: 'Pickup Location' },
                    { color: 'bg-primary-500', label: 'Driver Location (Live)' },
                    { color: 'bg-red-500', label: 'Delivery Address' }
                  ].map((item, index) => (
                    <motion.div
                      key={item.label}
                      className="flex items-center gap-3 text-sm"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <motion.div
                        className={`w-4 h-4 ${item.color} rounded-full shadow-lg`}
                        animate={{
                          scale: [1, 1.2, 1],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: index * 0.3
                        }}
                      />
                      <span className="text-white/90 font-medium">{item.label}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PackageTrackingPage;