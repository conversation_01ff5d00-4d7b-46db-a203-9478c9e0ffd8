import { useEffect } from 'react';
import { useCurrentUserData } from '../components/useCurrentUserData';
import { apiService } from '../services/apiService';

/**
 * Hook that initializes user authentication state on app start
 * This should be called at the root of the app
 */
export const useAuthInitialization = () => {
  const { user, setCurrentUser, clearUser } = useCurrentUserData();

  useEffect(() => {
    const initializeAuth = async () => {
      console.log('🚀 useAuthInitialization: Starting authentication check...');
      
      try {
        // Check if we have valid tokens
        const isAuth = await apiService.isAuthenticated();
        console.log('🔐 Authentication status:', isAuth);

        if (isAuth) {
          // If we don't have user data in store, try to load it
          if (!user) {
            console.log('🔄 No user in store, attempting to load profile...');
            try {
              const response = await apiService.getCurrentUser();
              if (response.success && response.data) {
                console.log('✅ User profile loaded:', response.data.user);
                setCurrentUser(response.data.user);
              } else {
                console.log('❌ Failed to load user profile:', response.message);
                clearUser();
              }
            } catch (error) {
              console.log('❌ Error loading user profile:', error);
              clearUser();
            }
          } else {
            console.log('✅ User already in store:', user.email);
          }
        } else {
          console.log('❌ Not authenticated, clearing user data');
          clearUser();
        }
      } catch (error) {
        console.log('❌ Authentication initialization error:', error);
        clearUser();
      }
    };

    initializeAuth();
  }, []); // Run only once on mount

  return { user };
};
