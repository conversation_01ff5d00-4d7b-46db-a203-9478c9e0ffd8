import mongoose from 'mongoose';
import { Service } from '../models/Service';
import { Category } from '../models/Category';
import { Supplier } from '../models/Supplier';
import connectDB from '../config/database';

// Static data from frontend
const servicesData = [
  {
    key: 'shopOrder',
    label: 'Order from Supplier',
    icon: 'storefront-outline',
    color: '#4A90E2',
    route: 'home/supplier-categories',
    order: 1
  },
  {
    key: 'sendPackage',
    label: 'Send a Package',
    icon: 'cube-outline',
    color: '#F5A623',
    route: 'home/send-package',
    order: 2
  },
  {
    key: 'requestPickup',
    label: 'Request Pickup',
    icon: 'arrow-redo-outline',
    color: '#50E3C2',
    route: '/home/<USER>',
    order: 3
  },
  {
    key: 'customDelivery',
    label: 'AI Chat System',
    icon: 'chatbubble-ellipses-outline',
    color: '#BD10E0',
    route: '/ai-chat',
    order: 4
  },
  {
    key: 'trackOrders',
    label: 'Track My Orders',
    icon: 'locate-outline',
    color: '#B8E986',
    route: '/orders',
    order: 5
  },
  {
    key: 'trackPackages',
    label: 'Track My Packages',
    icon: 'locate-outline',
    color: '#68E11f',
    route: '/packages',
    order: 6
  }
];

const categoriesData = [
  {
    key: 'restaurants',
    label: 'Restaurants',
    icon: 'fast-food-outline',
    color: '#FF6B6B',
    image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop&crop=center',
    bgGradient: 'from-red-500 via-orange-500 to-yellow-500',
    shadowColor: 'shadow-red-500/25',
    subtitle: 'Delicious meals delivered fast',
    badge: 'Popular',
    badgeColor: 'bg-red-100 text-red-800',
    route: {
      pathname: 'home/suppliers-page',
      params: { category: 'restaurants' }
    },
    order: 1
  },
  {
    key: 'clothings',
    label: 'Clothings',
    icon: 'shirt-outline',
    color: '#4ECDC4',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop&crop=center',
    bgGradient: 'from-teal-500 via-cyan-500 to-blue-500',
    shadowColor: 'shadow-teal-500/25',
    subtitle: 'Fashion & style for everyone',
    badge: 'Trending',
    badgeColor: 'bg-teal-100 text-teal-800',
    route: {
      pathname: 'home/suppliers-page',
      params: { category: 'clothings' }
    },
    order: 2
  },
  {
    key: 'pharmacies',
    label: 'Pharmacies',
    icon: 'medkit-outline',
    color: '#45B7D1',
    image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&crop=center',
    bgGradient: 'from-blue-500 via-indigo-500 to-purple-500',
    shadowColor: 'shadow-blue-500/25',
    subtitle: 'Health & wellness products',
    badge: 'Essential',
    badgeColor: 'bg-blue-100 text-blue-800',
    route: {
      pathname: 'home/suppliers-page',
      params: { category: 'pharmacies' }
    },
    order: 3
  },
  {
    key: 'supermarkets',
    label: 'Supermarkets',
    icon: 'cart-outline',
    color: '#96CEB4',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&crop=center',
    bgGradient: 'from-green-500 via-emerald-500 to-teal-500',
    shadowColor: 'shadow-green-500/25',
    subtitle: 'Fresh groceries & daily needs',
    badge: 'Fresh',
    badgeColor: 'bg-green-100 text-green-800',
    route: {
      pathname: 'home/suppliers-page',
      params: { category: 'supermarkets' }
    },
    order: 4
  }
];

const suppliersData = [
  {
    id: '3a-kefak',
    name: '3a kefak',
    lat: 32.22435557117164,
    lng: 35.23260147339766,
    category: 'restaurants',
    rating: 3.9,
    tags: ['Fast Food', 'Shawarma'],
    logoUrl: 'https://yummy.ps/upload/1736769086-kefak-2.jpg',
    banner: 'https://lh3.googleusercontent.com/gps-cs-s/AC9h4nr41CJ6Hgp5JfyuzqQ2ao-3EeyGbhIcpCLt19y11C7Q6oWcZ2dh_trnwdNjQtlGfYXc1Wzg110ITlO2uwAP7GS-drEvA_I1mmQQ9B6__BSGdox-9b-eKhXWn75vDJQQ3L_E1nEu=s680-w680-h510-rw',
    openHours: '10:00 AM - 11:00 PM',
    deliveryTime: '30-45 mins',
    phone: '+970593456789',
    products: [
      {
        id: 'shawerma-roll-1',
        name: 'لفة شاورما لحم',
        image: 'https://lh3.googleusercontent.com/gps-cs-s/AC9h4no04kdR0zPUHIB73R0plxIeGW8958ndyxg2NSwq1FJYkm-xochLZRABnU7iAO3YxBkYbsYLnGT_5ZMhArA6sFKuxn1AZMMGteNpycJrvT-p6OE3UoZkud9woKfnkJECZphG4WOD=w141-h235-n-k-no-nu',
        price: 18,
        discountPrice: 0,
        category: 'Shawarma',
        isAvailable: true,
        restaurantOptions: {
          additions: [
            { id: 'extra-meat', name: 'زيادة لحم', price: 5 },
            { id: 'extra-sauce', name: 'زيادة صوص', price: 2 },
            { id: 'extra-cheese', name: 'زيادة جبنة', price: 3 }
          ],
          without: ['بصل', 'مخللات', 'طحينية'],
          sides: [
            { id: 'fries', name: 'بطاطا مقلية', price: 8 },
            { id: 'drink', name: 'مشروب غازي', price: 5 },
            { id: 'salad', name: 'سلطة', price: 6 }
          ]
        }
      },
      {
        id: 'shawerma-chicken-1',
        name: 'لفة شاورما دجاج',
        image: 'https://lh3.googleusercontent.com/gps-cs-s/AC9h4no04kdR0zPUHIB73R0plxIeGW8958ndyxg2NSwq1FJYkm-xochLZRABnU7iAO3YxBkYbsYLnGT_5ZMhArA6sFKuxn1AZMMGteNpycJrvT-p6OE3UoZkud9woKfnkJECZphG4WOD=w141-h235-n-k-no-nu',
        price: 16,
        discountPrice: 0,
        category: 'Shawarma',
        isAvailable: true,
        restaurantOptions: {
          additions: [
            { id: 'extra-chicken', name: 'زيادة دجاج', price: 4 },
            { id: 'extra-garlic', name: 'زيادة ثوم', price: 1 },
            { id: 'extra-pickles', name: 'زيادة مخللات', price: 1 }
          ],
          without: ['بصل', 'خس', 'طماطم'],
          sides: [
            { id: 'fries', name: 'بطاطا مقلية', price: 8 },
            { id: 'drink', name: 'مشروب غازي', price: 5 },
            { id: 'hummus', name: 'حمص', price: 4 }
          ]
        }
      },
      {
        id: 'falafel-sandwich',
        name: 'ساندويش فلافل',
        image: 'https://images.unsplash.com/photo-1593504049359-74330189a345?w=400&h=400&fit=crop',
        price: 12,
        discountPrice: 0,
        category: 'Sandwiches',
        isAvailable: true,
        restaurantOptions: {
          additions: [
            { id: 'extra-falafel', name: 'زيادة فلافل', price: 3 },
            { id: 'extra-tahini', name: 'زيادة طحينية', price: 1 }
          ],
          without: ['طماطم', 'خيار', 'بقدونس'],
          sides: [
            { id: 'fries', name: 'بطاطا مقلية', price: 8 },
            { id: 'drink', name: 'مشروب غازي', price: 5 }
          ]
        }
      },
      {
        id: 'mixed-grill',
        name: 'مشاوي مشكلة',
        image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400&h=400&fit=crop',
        price: 45,
        discountPrice: 5,
        category: 'Grills',
        isAvailable: true,
        restaurantOptions: {
          additions: [
            { id: 'extra-kabab', name: 'زيادة كباب', price: 8 },
            { id: 'extra-tikka', name: 'زيادة تكة', price: 10 }
          ],
          without: ['بصل مشوي', 'فلفل حار'],
          sides: [
            { id: 'rice', name: 'أرز', price: 6 },
            { id: 'bread', name: 'خبز', price: 2 },
            { id: 'salad', name: 'سلطة', price: 6 },
            { id: 'drink', name: 'مشروب غازي', price: 5 }
          ]
        }
      }
    ]
  },
  {
    id: 'style-point',
    name: 'Style Point',
    lat: 32.22435557117164,
    lng: 35.23260147339766,
    category: 'clothings',
    rating: 4.2,
    tags: ['Fashion', 'Clothing'],
    logoUrl: 'https://example.com/style-point-logo.jpg',
    banner: 'https://example.com/style-point-banner.jpg',
    openHours: '9:00 AM - 10:00 PM',
    deliveryTime: '1-2 hours',
    phone: '+970591234567',
    products: [
      {
        id: 'jacket-1',
        name: 'Turkesh Jacket',
        image: 'https://brandsmegastore.co.za/cdn/shop/files/356a0db49a2328d8cb4f40fcfc23322e-Photoroom.png?v=1746437670',
        price: 200,
        discountPrice: 20,
        category: 'Jackets',
        isAvailable: true,
        clothingOptions: {
          sizes: ['S', 'M', 'L', 'XL'],
          colors: ['$gray12', 'brown'],
          gallery: [
            "https://brandsmegastore.co.za/cdn/shop/files/356a0db49a2328d8cb4f40fcfc23322e-Photoroom.png?v=1746437670",
            "https://media.6media.me/media/catalog/product/l/c/lcw-w12489z8-jjl-3.jpg"
          ]
        }
      }
    ]
  }
];

export async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to database
    await connectDB();

    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    await Service.deleteMany({});
    await Category.deleteMany({});
    await Supplier.deleteMany({});

    // Seed services
    console.log('📋 Seeding services...');
    await Service.insertMany(servicesData);
    console.log(`✅ Seeded ${servicesData.length} services`);

    // Seed categories
    console.log('🏷️  Seeding categories...');
    await Category.insertMany(categoriesData);
    console.log(`✅ Seeded ${categoriesData.length} categories`);

    // Seed suppliers
    console.log('🏪 Seeding suppliers...');
    await Supplier.insertMany(suppliersData);
    console.log(`✅ Seeded ${suppliersData.length} suppliers`);

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
